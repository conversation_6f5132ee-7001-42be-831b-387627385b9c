# Scratch项目改编功能 - 复用现有历史记录系统设计

## 1. 现有历史记录系统分析

### 1.1 现有History模型结构
```python
class History(Base):
    """历史数据模型"""
    __tablename__ = "history"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    content_type = Column(String(20), nullable=False, index=True)  # "article", "video"
    content_id = Column(Integer, nullable=False, index=True)
    created_at = Column(Timestamp, default=now_utc, index=True)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc)
    last_visited_at = Column(Timestamp, default=now_utc)  # 最后访问时间
    visit_count = Column(Integer, default=1)  # 访问次数
    
    # 关联关系
    user = relationship("User")
    
    # 联合唯一约束：同一用户对同一内容只能记录一次
    __table_args__ = (
        UniqueConstraint("user_id", "content_type", "content_id", name="uq_user_content_history"),
    )
```

### 1.2 系统支持的内容类型
- `article` - 文章
- `video` - 视频
- **新增**: `scratch_project` - Scratch项目

## 2. 复用现有系统的优势

### 2.1 架构一致性
- **统一的历史记录接口**: 所有内容类型使用相同的历史记录机制
- **代码复用**: 可以复用现有的CRUD操作、API端点、缓存策略
- **维护简便**: 统一的数据结构便于维护和扩展

### 2.2 功能完整性
- **访问计数**: 自动记录用户访问Scratch项目的次数
- **时间追踪**: 记录首次访问和最后访问时间
- **去重机制**: 同一用户对同一项目只保留一条历史记录

### 2.3 性能优化
- **现有索引**: 已有的数据库索引可以直接支持Scratch项目查询
- **缓存策略**: 可以复用现有的Redis缓存机制
- **分页支持**: 现有的游标分页系统可以直接使用

## 3. Scratch项目改编功能集成方案

### 3.1 扩展content_type支持

#### 3.1.1 更新Schema定义
```python
# app/schemas/history.py
from typing import Literal

class HistoryBase(BaseModel):
    content_type: Literal["article", "video", "scratch_project"]  # 新增scratch_project
    content_id: int

class HistoryCreate(HistoryBase):
    pass

class HistoryUpdate(BaseModel):
    last_visited_at: datetime | None = None
    visit_count: int | None = None

class History(HistoryBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    last_visited_at: datetime
    visit_count: int

    class Config:
        from_attributes = True
```

#### 3.1.2 更新CRUD操作
```python
# app/crud/history.py (扩展现有功能)
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

class HistoryCRUD:
    
    async def get_user_scratch_history(
        self, 
        db: AsyncSession, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 20
    ) -> List[History]:
        """获取用户的Scratch项目访问历史"""
        result = await db.execute(
            select(History)
            .where(
                and_(
                    History.user_id == user_id,
                    History.content_type == "scratch_project"
                )
            )
            .order_by(History.last_visited_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def record_scratch_visit(
        self,
        db: AsyncSession,
        user_id: int,
        scratch_project_id: int
    ) -> History:
        """记录用户访问Scratch项目"""
        # 查找是否已有记录
        existing = await db.execute(
            select(History).where(
                and_(
                    History.user_id == user_id,
                    History.content_type == "scratch_project",
                    History.content_id == scratch_project_id
                )
            )
        )
        history = existing.scalar_one_or_none()
        
        if history:
            # 更新现有记录
            history.last_visited_at = now_utc()
            history.visit_count += 1
            history.updated_at = now_utc()
        else:
            # 创建新记录
            history = History(
                user_id=user_id,
                content_type="scratch_project",
                content_id=scratch_project_id
            )
            db.add(history)
        
        await db.commit()
        await db.refresh(history)
        return history
```

### 3.2 集成到Scratch服务层

#### 3.2.1 更新ScratchAdaptationService
```python
# app/services/scratch_adaptation_service.py (扩展现有服务)
from app.crud.history import history_crud

class ScratchAdaptationService:
    
    async def get_project_with_history(
        self,
        db: AsyncSession,
        project_id: int,
        current_user_id: int | None = None
    ) -> dict:
        """获取项目详情并记录访问历史"""
        project = await scratch_crud.get(db, id=project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # 记录访问历史（如果用户已登录）
        if current_user_id:
            await history_crud.record_scratch_visit(
                db=db,
                user_id=current_user_id,
                scratch_project_id=project_id
            )
        
        # 获取项目详细信息（包括改编关系）
        project_data = await self.get_project_with_adaptations(db, project_id)
        
        return project_data
    
    async def get_user_scratch_history(
        self,
        db: AsyncSession,
        user_id: int,
        skip: int = 0,
        limit: int = 20
    ) -> dict:
        """获取用户的Scratch项目访问历史"""
        histories = await history_crud.get_user_scratch_history(
            db=db, user_id=user_id, skip=skip, limit=limit
        )
        
        # 获取项目详细信息
        project_ids = [h.content_id for h in histories]
        projects = await scratch_crud.get_multi_by_ids(db, ids=project_ids)
        project_dict = {p.id: p for p in projects}
        
        result = []
        for history in histories:
            project = project_dict.get(history.content_id)
            if project:
                result.append({
                    "history": history,
                    "project": project,
                    "last_visited": history.last_visited_at,
                    "visit_count": history.visit_count
                })
        
        return {
            "items": result,
            "total": len(result),
            "skip": skip,
            "limit": limit
        }
```

### 3.3 更新API端点

#### 3.3.1 扩展现有历史API
```python
# app/api/endpoints/history.py (扩展现有端点)
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()

@router.get("/users/{user_id}/history/scratch", response_model=dict)
async def get_user_scratch_history(
    user_id: int,
    skip: int = 0,
    limit: int = 20,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取用户的Scratch项目访问历史"""
    # 权限检查
    if current_user.id != user_id and not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Permission denied")
    
    return await scratch_adaptation_service.get_user_scratch_history(
        db=db, user_id=user_id, skip=skip, limit=limit
    )

@router.post("/scratch/{project_id}/visit")
async def record_scratch_visit(
    project_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """记录用户访问Scratch项目"""
    await history_crud.record_scratch_visit(
        db=db,
        user_id=current_user.id,
        scratch_project_id=project_id
    )
    return {"message": "Visit recorded successfully"}
```

#### 3.3.2 更新Scratch项目API
```python
# app/api/endpoints/scratch.py (更新现有端点)

@router.get("/{project_id}", response_model=ScratchProjectResponse)
async def get_scratch_project(
    project_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User | None = Depends(get_current_user_optional)
):
    """获取Scratch项目详情（自动记录访问历史）"""
    current_user_id = current_user.id if current_user else None
    
    project_data = await scratch_adaptation_service.get_project_with_history(
        db=db,
        project_id=project_id,
        current_user_id=current_user_id
    )
    
    return project_data
```

## 4. 数据迁移方案

### 4.1 无需额外迁移
由于复用现有的 `History` 表，不需要创建新的数据库表或迁移脚本。只需要：

1. **更新应用代码**: 添加对 `scratch_project` content_type的支持
2. **更新Schema验证**: 扩展content_type的枚举值
3. **测试现有索引**: 确认现有索引能够支持新的查询模式

### 4.2 兼容性保证
- **向后兼容**: 现有的article和video历史记录不受影响
- **渐进式部署**: 可以逐步启用Scratch项目的历史记录功能
- **数据完整性**: 利用现有的唯一约束保证数据一致性

## 5. 缓存策略集成

### 5.1 复用现有缓存
```python
# app/services/cache_service.py (扩展现有缓存)
class CacheService:
    
    @staticmethod
    def get_user_scratch_history_key(user_id: int) -> str:
        """用户Scratch历史记录缓存键"""
        return f"user:history:scratch:{user_id}"
    
    @staticmethod
    def get_scratch_visit_count_key(project_id: int) -> str:
        """Scratch项目访问统计缓存键"""
        return f"scratch:visits:{project_id}"
    
    async def cache_user_scratch_history(
        self,
        user_id: int,
        history_data: dict,
        ttl: int = 3600
    ):
        """缓存用户Scratch历史记录"""
        key = self.get_user_scratch_history_key(user_id)
        await self.redis.setex(
            key,
            ttl,
            json.dumps(history_data, default=str)
        )
    
    async def get_cached_user_scratch_history(self, user_id: int) -> dict | None:
        """获取缓存的用户Scratch历史记录"""
        key = self.get_user_scratch_history_key(user_id)
        cached_data = await self.redis.get(key)
        if cached_data:
            return json.loads(cached_data)
        return None
```

## 6. 监控和分析

### 6.1 复用现有分析工具
- **访问统计**: 利用现有的History表分析用户行为
- **热门项目**: 基于visit_count统计最受欢迎的Scratch项目
- **用户粘性**: 分析用户对Scratch项目的重复访问模式

### 6.2 新增Scratch特有指标
```python
# app/services/analytics_service.py
class ScratchAnalyticsService:
    
    async def get_popular_scratch_projects(
        self,
        db: AsyncSession,
        limit: int = 10
    ) -> List[dict]:
        """获取最受欢迎的Scratch项目"""
        result = await db.execute(
            select(
                History.content_id,
                func.sum(History.visit_count).label('total_visits'),
                func.count(History.id).label('unique_visitors')
            )
            .where(History.content_type == "scratch_project")
            .group_by(History.content_id)
            .order_by(func.sum(History.visit_count).desc())
            .limit(limit)
        )
        
        stats = result.all()
        project_ids = [stat.content_id for stat in stats]
        projects = await scratch_crud.get_multi_by_ids(db, ids=project_ids)
        
        return [
            {
                "project": next(p for p in projects if p.id == stat.content_id),
                "total_visits": stat.total_visits,
                "unique_visitors": stat.unique_visitors
            }
            for stat in stats
        ]
```

## 7. 实施步骤

### 7.1 第一阶段：基础集成
1. 更新Schema定义，添加`scratch_project`支持
2. 扩展CRUD操作，添加Scratch特有的查询方法
3. 更新服务层，集成历史记录功能

### 7.2 第二阶段：API集成
1. 扩展现有历史API端点
2. 更新Scratch项目API，自动记录访问
3. 添