# Scratch项目改编功能 - 剩余任务实现文档

## 概述

本文档详细描述了Scratch项目改编功能剩余3个任务的实现方案，包括改编历史追踪、缓存优化和API文档。

---

## 任务13：改编历史和关系追踪功能

### 13.1 功能需求

#### 13.1.1 改编历史记录
- 记录所有改编操作的详细日志
- 追踪项目改编关系的变化
- 记录用户改编行为和统计数据
- 支持改编活动的审计和分析

#### 13.1.2 关系追踪
- 实时维护改编关系图
- 追踪改编链的完整性
- 监控改编树的结构变化
- 支持关系数据的快速查询

### 13.2 数据库设计

#### 13.2.1 改编历史表 (scratch_adaptation_history)

```sql
CREATE TABLE scratch_adaptation_history (
    id BIGSERIAL PRIMARY KEY,
    project_id BIGINT NOT NULL REFERENCES scratch_products(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL, -- CREATE, UPDATE, DELETE, ADAPT
    original_project_id BIGINT REFERENCES scratch_products(id) ON DELETE SET NULL,
    target_project_id BIGINT REFERENCES scratch_products(id) ON DELETE SET NULL,
    adaptation_type VARCHAR(50),
    metadata JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_history_project_id (project_id),
    INDEX idx_history_user_id (user_id),
    INDEX idx_history_action_type (action_type),
    INDEX idx_history_created_at (created_at),
    INDEX idx_history_metadata (metadata) USING GIN
);
```

#### 13.2.2 改编关系快照表 (scratch_adaptation_snapshots)

```sql
CREATE TABLE scratch_adaptation_snapshots (
    id BIGSERIAL PRIMARY KEY,
    project_id BIGINT NOT NULL REFERENCES scratch_products(id) ON DELETE CASCADE,
    adaptation_chain JSONB NOT NULL, -- 完整的改编链数据
    adaptation_tree JSONB NOT NULL, -- 改编树结构数据
    statistics JSONB NOT NULL, -- 统计信息
    snapshot_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_snapshots_project_id (project_id),
    INDEX idx_snapshots_time (snapshot_time),
    INDEX idx_snapshots_chain (adaptation_chain) USING GIN,
    INDEX idx_snapshots_tree (adaptation_tree) USING GIN
);
```

### 13.3 模型实现

#### 13.3.1 历史记录模型

```python
# app/models/scratch_history.py
from sqlalchemy import Column, BigInteger, String, Text, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import INET, JSONB
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class ScratchAdaptationHistory(Base):
    __tablename__ = "scratch_adaptation_history"
    
    id = Column(BigInteger, primary_key=True, index=True)
    project_id = Column(BigInteger, ForeignKey("scratch_products.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    action_type = Column(String(50), nullable=False)
    original_project_id = Column(BigInteger, ForeignKey("scratch_products.id", ondelete="SET NULL"))
    target_project_id = Column(BigInteger, ForeignKey("scratch_products.id", ondelete="SET NULL"))
    adaptation_type = Column(String(50))
    metadata = Column(JSONB)
    ip_address = Column(INET)
    user_agent = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系定义
    project = relationship("ScratchProduct", foreign_keys=[project_id])
    user = relationship("User")
    original_project = relationship("ScratchProduct", foreign_keys=[original_project_id])
    target_project = relationship("ScratchProduct", foreign_keys=[target_project_id])

class ScratchAdaptationSnapshot(Base):
    __tablename__ = "scratch_adaptation_snapshots"
    
    id = Column(BigInteger, primary_key=True, index=True)
    project_id = Column(BigInteger, ForeignKey("scratch_products.id", ondelete="CASCADE"), nullable=False)
    adaptation_chain = Column(JSONB, nullable=False)
    adaptation_tree = Column(JSONB, nullable=False)
    statistics = Column(JSONB, nullable=False)
    snapshot_time = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系定义
    project = relationship("ScratchProduct")
```

#### 13.3.2 Schema定义

```python
# app/schemas/scratch_history.py
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum

class HistoryActionType(str, Enum):
    CREATE = "CREATE"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    ADAPT = "ADAPT"
    PUBLISH = "PUBLISH"
    UNPUBLISH = "UNPUBLISH"

class ScratchAdaptationHistoryBase(BaseModel):
    project_id: int
    user_id: int
    action_type: HistoryActionType
    original_project_id: Optional[int] = None
    target_project_id: Optional[int] = None
    adaptation_type: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

class ScratchAdaptationHistoryCreate(ScratchAdaptationHistoryBase):
    pass

class ScratchAdaptationHistoryResponse(ScratchAdaptationHistoryBase):
    id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class AdaptationChainNode(BaseModel):
    project_id: int
    title: str
    author_name: str
    created_at: datetime
    adaptation_type: Optional[str] = None
    level: int

class AdaptationTreeNode(BaseModel):
    project_id: int
    title: str
    author_name: str
    created_at: datetime
    adaptation_type: Optional[str] = None
    level: int
    children: List['AdaptationTreeNode'] = []

class ScratchAdaptationSnapshotResponse(BaseModel):
    id: int
    project_id: int
    adaptation_chain: List[AdaptationChainNode]
    adaptation_tree: AdaptationTreeNode
    statistics: Dict[str, Any]
    snapshot_time: datetime
    
    class Config:
        from_attributes = True
```

### 13.4 CRUD操作实现

```python
# app/crud/scratch_history.py
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from app.crud.base import CRUDBase
from app.models.scratch_history import ScratchAdaptationHistory, ScratchAdaptationSnapshot
from app.schemas.scratch_history import ScratchAdaptationHistoryCreate

class CRUDScratchAdaptationHistory(CRUDBase[ScratchAdaptationHistory, ScratchAdaptationHistoryCreate, None]):
    
    def get_by_project(
        self, 
        db: Session, 
        project_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[ScratchAdaptationHistory]:
        """获取项目的改编历史记录"""
        return db.query(ScratchAdaptationHistory)\
                 .filter(ScratchAdaptationHistory.project_id == project_id)\
                 .order_by(desc(ScratchAdaptationHistory.created_at))\
                 .offset(skip)\
                 .limit(limit)\
                 .all()
    
    def get_by_user(
        self, 
        db: Session, 
        user_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[ScratchAdaptationHistory]:
        """获取用户的改编历史记录"""
        return db.query(ScratchAdaptationHistory)\
                 .filter(ScratchAdaptationHistory.user_id == user_id)\
                 .order_by(desc(ScratchAdaptationHistory.created_at))\
                 .offset(skip)\
                 .limit(limit)\
                 .all()
    
    def get_adaptation_activities(
        self,
        db: Session,
        project_id: int,
        action_types: Optional[List[str]] = None
    ) -> List[ScratchAdaptationHistory]:
        """获取项目的改编活动记录"""
        query = db.query(ScratchAdaptationHistory).filter(
            or_(
                ScratchAdaptationHistory.project_id == project_id,
                ScratchAdaptationHistory.original_project_id == project_id,
                ScratchAdaptationHistory.target_project_id == project_id
            )
        )
        
        if action_types:
            query = query.filter(ScratchAdaptationHistory.action_type.in_(action_types))
        
        return query.order_by(desc(ScratchAdaptationHistory.created_at)).all()
    
    def create_history_record(
        self,
        db: Session,
        project_id: int,
        user_id: int,
        action_type: str,
        **kwargs
    ) -> ScratchAdaptationHistory:
        """创建历史记录"""
        history_data = {
            "project_id": project_id,
            "user_id": user_id,
            "action_type": action_type,
            **kwargs
        }
        
        history_record = ScratchAdaptationHistory(**history_data)
        db.add(history_record)
        db.commit()
        db.refresh(history_record)
        return history_record

class CRUDScratchAdaptationSnapshot(CRUDBase[ScratchAdaptationSnapshot, None, None]):
    
    def create_snapshot(
        self,
        db: Session,
        project_id: int,
        adaptation_chain: List[Dict[str, Any]],
        adaptation_tree: Dict[str, Any],
        statistics: Dict[str, Any]
    ) -> ScratchAdaptationSnapshot:
        """创建改编关系快照"""
        snapshot = ScratchAdaptationSnapshot(
            project_id=project_id,
            adaptation_chain=adaptation_chain,
            adaptation_tree=adaptation_tree,
            statistics=statistics
        )
        db.add(snapshot)
        db.commit()
        db.refresh(snapshot)
        return snapshot
    
    def get_latest_snapshot(
        self,
        db: Session,
        project_id: int
    ) -> Optional[ScratchAdaptationSnapshot]:
        """获取最新的快照"""
        return db.query(ScratchAdaptationSnapshot)\
                 .filter(ScratchAdaptationSnapshot.project_id == project_id)\
                 .order_by(desc(ScratchAdaptationSnapshot.snapshot_time))\
                 .first()

# 实例化CRUD对象
scratch_adaptation_history = CRUDScratchAdaptationHistory(ScratchAdaptationHistory)
scratch_adaptation_snapshot = CRUDScratchAdaptationSnapshot(ScratchAdaptationSnapshot)
```

### 13.5 服务层实现

```python
# app/services/scratch_history_service.py
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from app.crud.scratch_history import scratch_adaptation_history, scratch_adaptation_snapshot
from app.crud.scratch import scratch_product
from app.schemas.scratch_history import (
    ScratchAdaptationHistoryCreate, 
    HistoryActionType,
    AdaptationChainNode,
    AdaptationTreeNode
)
import logging

logger = logging.getLogger(__name__)

class ScratchHistoryService:
    """改编历史追踪服务"""
    
    def __init__(self):
        self.history_crud = scratch_adaptation_history
        self.snapshot_crud = scratch_adaptation_snapshot
        self.project_crud = scratch_product
    
    async def record_adaptation_activity(
        self,
        db: Session,
        project_id: int,
        user_id: int,
        action_type: HistoryActionType,
        original_project_id: Optional[int] = None,
        target_project_id: Optional[int] = None,
        adaptation_type: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """记录改编活动"""
        try:
            history_record = self.history_crud.create_history_record(
                db=db,
                project_id=project_id,
                user_id=user_id,
                action_type=action_type.value,
                original_project_id=original_project_id,
                target_project_id=target_project_id,
                adaptation_type=adaptation_type,
                metadata=metadata or {},
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # 如果是改编操作，创建快照
            if action_type == HistoryActionType.ADAPT:
                await self._create_adaptation_snapshot(db, project_id)
            
            logger.info(f"Recorded adaptation activity: {action_type} for project {project_id}")
            return history_record
            
        except Exception as e:
            logger.error(f"Failed to record adaptation activity: {e}")
            raise
    
    async def _create_adaptation_snapshot(self, db: Session, project_id: int):
        """创建改编关系快照"""
        try:
            # 获取改编链
            adaptation_chain = await self.project_crud.get_adaptation_chain(db, project_id)
            
            # 构建改编树
            adaptation_tree = await self._build_adaptation_tree(db, project_id)
            
            # 计算统计信息
            statistics = await self._calculate_adaptation_statistics(db, project_id)
            
            # 创建快照
            snapshot = self.snapshot_crud.create_snapshot(
                db=db,
                project_id=project_id,
                adaptation_chain=adaptation_chain,
                adaptation_tree=adaptation_tree,
                statistics=statistics
            )
            
            logger.info(f"Created adaptation snapshot for project {project_id}")
            return snapshot
            
        except Exception as e:
            logger.error(f"Failed to create adaptation snapshot: {e}")
            raise

# 实例化服务
scratch_history_service = ScratchHistoryService()
```

### 13.6 数据库迁移脚本

```python
# alembic/versions/scratch
_adaptation_history_002.py
"""Add scratch adaptation history tables

Revision ID: scratch_adaptation_history_002
Revises: scratch_adaptation_001
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = 'scratch_adaptation_history_002'
down_revision = 'scratch_adaptation_001'
branch_labels = None
depends_on = None

def upgrade():
    # 创建改编历史表
    op.create_table(
        'scratch_adaptation_history',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('project_id', sa.BigInteger(), nullable=False),
        sa.Column('user_id', sa.BigInteger(), nullable=False),
        sa.Column('action_type', sa.String(length=50), nullable=False),
        sa.Column('original_project_id', sa.BigInteger(), nullable=True),
        sa.Column('target_project_id', sa.BigInteger(), nullable=True),
        sa.Column('adaptation_type', sa.String(length=50), nullable=True),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('ip_address', postgresql.INET(), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['original_project_id'], ['scratch_products.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['project_id'], ['scratch_products.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['target_project_id'], ['scratch_products.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建索引
    op.create_index('idx_history_project_id', 'scratch_adaptation_history', ['project_id'])
    op.create_index('idx_history_user_id', 'scratch_adaptation_history', ['user_id'])
    op.create_index('idx_history_action_type', 'scratch_adaptation_history', ['action_type'])
    op.create_index('idx_history_created_at', 'scratch_adaptation_history', ['created_at'])
    op.create_index('idx_history_metadata', 'scratch_adaptation_history', ['metadata'], postgresql_using='gin')
    
    # 创建改编快照表
    op.create_table(
        'scratch_adaptation_snapshots',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('project_id', sa.BigInteger(), nullable=False),
        sa.Column('adaptation_chain', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('adaptation_tree', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('statistics', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('snapshot_time', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['project_id'], ['scratch_products.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建快照表索引
    op.create_index('idx_snapshots_project_id', 'scratch_adaptation_snapshots', ['project_id'])
    op.create_index('idx_snapshots_time', 'scratch_adaptation_snapshots', ['snapshot_time'])
    op.create_index('idx_snapshots_chain', 'scratch_adaptation_snapshots', ['adaptation_chain'], postgresql_using='gin')
    op.create_index('idx_snapshots_tree', 'scratch_adaptation_snapshots', ['adaptation_tree'], postgresql_using='gin')

def downgrade():
    # 删除快照表
    op.drop_index('idx_snapshots_tree', table_name='scratch_adaptation_snapshots')
    op.drop_index('idx_snapshots_chain', table_name='scratch_adaptation_snapshots')
    op.drop_index('idx_snapshots_time', table_name='scratch_adaptation_snapshots')
    op.drop_index('idx_snapshots_project_id', table_name='scratch_adaptation_snapshots')
    op.drop_table('scratch_adaptation_snapshots')
    
    # 删除历史表
    op.drop_index('idx_history_metadata', table_name='scratch_adaptation_history')
    op.drop_index('idx_history_created_at', table_name='scratch_adaptation_history')
    op.drop_index('idx_history_action_type', table_name='scratch_adaptation_history')
    op.drop_index('idx_history_user_id', table_name='scratch_adaptation_history')
    op.drop_index('idx_history_project_id', table_name='scratch_adaptation_history')
    op.drop_table('scratch_adaptation_history')
```

---

## 任务14：集成缓存和性能优化

### 14.1 缓存架构设计

#### 14.1.1 缓存策略
- **Redis集群**: 主从复制 + 哨兵模式
- **多层缓存**: L1(内存) + L2(Redis) + L3(数据库)
- **缓存分区**: 按功能模块分离缓存键空间
- **TTL策略**: 差异化过期时间设置

#### 14.1.2 缓存键设计

```python
# 缓存键命名规范
CACHE_KEYS = {
    # 项目基础信息
    "project_info": "scratch:project:{project_id}",
    "project_list": "scratch:projects:user:{user_id}:page:{page}",
    
    # 改编关系
    "adaptation_chain": "scratch:chain:{project_id}",
    "adaptation_tree": "scratch:tree:{project_id}",
    "adaptation_stats": "scratch:stats:{project_id}",
    
    # 统计数据
    "user_stats": "scratch:user_stats:{user_id}",
    "global_stats": "scratch:global_stats",
    
    # 搜索结果
    "search_results": "scratch:search:{query_hash}:page:{page}",
}

# TTL设置 (秒)
CACHE_TTL = {
    "project_info": 3600,      # 1小时
    "project_list": 1800,      # 30分钟
    "adaptation_chain": 7200,  # 2小时
    "adaptation_tree": 7200,   # 2小时
    "adaptation_stats": 3600,  # 1小时
    "user_stats": 1800,        # 30分钟
    "global_stats": 600,       # 10分钟
    "search_results": 900,     # 15分钟
}
```

### 14.2 Redis配置和连接

#### 14.2.1 Redis配置文件

```yaml
# docker-compose.yml 中的Redis配置
version: '3.8'
services:
  redis-master:
    image: redis:7-alpine
    command: redis-server --appendonly yes --replica-read-only no
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    environment:
      - REDIS_REPLICATION_MODE=master
  
  redis-sentinel:
    image: redis:7-alpine
    command: redis-sentinel /usr/local/etc/redis/sentinel.conf
    volumes:
      - ./sentinel.conf:/usr/local/etc/redis/sentinel.conf
    depends_on:
      - redis-master

volumes:
  redis_data:
```

#### 14.2.2 Redis连接管理

```python
# app/core/redis.py
import redis
from redis.sentinel import Sentinel
from typing import Optional
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

class RedisManager:
    """Redis连接管理器"""
    
    def __init__(self):
        self._redis_client: Optional[redis.Redis] = None
        self._sentinel: Optional[Sentinel] = None
    
    def init_redis(self):
        """初始化Redis连接"""
        try:
            if settings.REDIS_SENTINEL_ENABLED:
                # 使用哨兵模式
                self._sentinel = Sentinel([
                    (settings.REDIS_SENTINEL_HOST, settings.REDIS_SENTINEL_PORT)
                ])
                self._redis_client = self._sentinel.master_for(
                    settings.REDIS_MASTER_NAME,
                    socket_timeout=settings.REDIS_TIMEOUT,
                    password=settings.REDIS_PASSWORD
                )
            else:
                # 单机模式
                self._redis_client = redis.Redis(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    password=settings.REDIS_PASSWORD,
                    db=settings.REDIS_DB,
                    decode_responses=True,
                    socket_timeout=settings.REDIS_TIMEOUT
                )
            
            # 测试连接
            self._redis_client.ping()
            logger.info("Redis connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis: {e}")
            raise
    
    @property
    def client(self) -> redis.Redis:
        """获取Redis客户端"""
        if not self._redis_client:
            self.init_redis()
        return self._redis_client
    
    def close(self):
        """关闭Redis连接"""
        if self._redis_client:
            self._redis_client.close()
            logger.info("Redis connection closed")

# 全局Redis管理器实例
redis_manager = RedisManager()
```

### 14.3 缓存服务实现

```python
# app/services/cache_service.py
import json
import hashlib
from typing import Any, Optional, List, Dict, Union
from datetime import datetime, timedelta
from app.core.redis import redis_manager
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

class CacheService:
    """缓存服务"""
    
    def __init__(self):
        self.redis = redis_manager.client
        self.default_ttl = 3600  # 1小时
    
    def _generate_key(self, template: str, **kwargs) -> str:
        """生成缓存键"""
        return template.format(**kwargs)
    
    def _serialize_value(self, value: Any) -> str:
        """序列化值"""
        if isinstance(value, (dict, list)):
            return json.dumps(value, default=str, ensure_ascii=False)
        return str(value)
    
    def _deserialize_value(self, value: str) -> Any:
        """反序列化值"""
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            value = self.redis.get(key)
            if value is None:
                return None
            return self._deserialize_value(value)
        except Exception as e:
            logger.warning(f"Failed to get cache key {key}: {e}")
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None
    ) -> bool:
        """设置缓存值"""
        try:
            serialized_value = self._serialize_value(value)
            ttl = ttl or self.default_ttl
            return self.redis.setex(key, ttl, serialized_value)
        except Exception as e:
            logger.warning(f"Failed to set cache key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        try:
            return bool(self.redis.delete(key))
        except Exception as e:
            logger.warning(f"Failed to delete cache key {key}: {e}")
            return False
    
    async def delete_pattern(self, pattern: str) -> int:
        """批量删除匹配模式的缓存键"""
        try:
            keys = self.redis.keys(pattern)
            if keys:
                return self.redis.delete(*keys)
            return 0
        except Exception as e:
            logger.warning(f"Failed to delete cache pattern {pattern}: {e}")
            return 0
    
    async def exists(self, key: str) -> bool:
        """检查缓存键是否存在"""
        try:
            return bool(self.redis.exists(key))
        except Exception as e:
            logger.warning(f"Failed to check cache key existence {key}: {e}")
            return False
    
    async def expire(self, key: str, ttl: int) -> bool:
        """设置缓存键过期时间"""
        try:
            return bool(self.redis.expire(key, ttl))
        except Exception as e:
            logger.warning(f"Failed to set expiration for key {key}: {e}")
            return False

# 全局缓存服务实例
cache_service = CacheService()
```

### 14.4 Scratch项目缓存实现

```python
# app/services/scratch_cache_service.py
import hashlib
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from app.services.cache_service import cache_service
from app.crud.scratch import scratch_product
from app.schemas.scratch import ScratchProductResponse
import logging

logger = logging.getLogger(__name__)

class ScratchCacheService:
    """Scratch项目缓存服务"""
    
    def __init__(self):
        self.cache = cache_service
        self.project_crud = scratch_product
    
    # 缓存键模板
    CACHE_KEYS = {
        "project_info": "scratch:project:{project_id}",
        "project_list": "scratch:projects:user:{user_id}:page:{page}:limit:{limit}",
        "adaptation_chain": "scratch:chain:{project_id}",
        "adaptation_tree": "scratch:tree:{project_id}",
        "adaptation_stats": "scratch:stats:{project_id}",
        "user_projects_count": "scratch:user_count:{user_id}",
        "search_results": "scratch:search:{query_hash}:page:{page}",
    }
    
    # TTL设置
    CACHE_TTL = {
        "project_info": 3600,      # 1小时
        "project_list": 1800,      # 30分钟
        "adaptation_chain": 7200,  # 2小时
        "adaptation_tree": 7200,   # 2小时
        "adaptation_stats": 3600,  # 1小时
        "user_projects_count": 1800,  # 30分钟
        "search_results": 900,     # 15分钟
    
    }
    
    async def get_project_info(
        self,
        db: Session,
        project_id: int,
        use_cache: bool = True
    ) -> Optional[Dict[str, Any]]:
        """获取项目信息（带缓存）"""
        if use_cache:
            cache_key = self.CACHE_KEYS["project_info"].format(project_id=project_id)
            cached_result = await self.cache.get(cache_key)
            if cached_result:
                logger.debug(f"Cache hit for project {project_id}")
                return cached_result
        
        # 从数据库获取
        project = self.project_crud.get(db, id=project_id)
        if not project:
            return None
        
        project_data = {
            "id": project.id,
            "title": project.title,
            "description": project.description,
            "author_id": project.author_id,
            "original_project_id": project.original_project_id,
            "root_project_id": project.root_project_id,
            "adapt_level": project.adapt_level,
            "adaptation_type": project.adaptation_type,
            "created_at": project.created_at.isoformat(),
            "updated_at": project.updated_at.isoformat(),
        }
        
        # 缓存结果
        if use_cache:
            cache_key = self.CACHE_KEYS["project_info"].format(project_id=project_id)
            await self.cache.set(
                cache_key,
                project_data,
                ttl=self.CACHE_TTL["project_info"]
            )
        
        return project_data
    
    async def get_adaptation_chain(
        self,
        db: Session,
        project_id: int,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """获取改编链（带缓存）"""
        if use_cache:
            cache_key = self.CACHE_KEYS["adaptation_chain"].format(project_id=project_id)
            cached_result = await self.cache.get(cache_key)
            if cached_result:
                logger.debug(f"Cache hit for adaptation chain {project_id}")
                return cached_result
        
        # 从数据库获取
        chain = await self.project_crud.get_adaptation_chain(db, project_id)
        chain_data = [
            {
                "project_id": item.id,
                "title": item.title,
                "author_name": item.author.username if item.author else "Unknown",
                "created_at": item.created_at.isoformat(),
                "adaptation_type": item.adaptation_type,
                "level": item.adapt_level or 0
            }
            for item in chain
        ]
        
        # 缓存结果
        if use_cache:
            cache_key = self.CACHE_KEYS["adaptation_chain"].format(project_id=project_id)
            await self.cache.set(
                cache_key,
                chain_data,
                ttl=self.CACHE_TTL["adaptation_chain"]
            )
        
        return chain_data
    
    async def invalidate_project_cache(self, project_id: int):
        """清除项目相关缓存"""
        patterns = [
            f"scratch:project:{project_id}",
            f"scratch:chain:{project_id}",
            f"scratch:tree:{project_id}",
            f"scratch:stats:{project_id}",
            f"scratch:projects:*",  # 清除项目列表缓存
        ]
        
        for pattern in patterns:
            await self.cache.delete_pattern(pattern)
        
        logger.info(f"Invalidated cache for project {project_id}")
    
    async def invalidate_user_cache(self, user_id: int):
        """清除用户相关缓存"""
        patterns = [
            f"scratch:projects:user:{user_id}:*",
            f"scratch:user_count:{user_id}",
        ]
        
        for pattern in patterns:
            await self.cache.delete_pattern(pattern)
        
        logger.info(f"Invalidated cache for user {user_id}")

# 全局Scratch缓存服务实例
scratch_cache_service = ScratchCacheService()
```

### 14.5 性能优化策略

#### 14.5.1 数据库查询优化

```python
# app/crud/scratch.py 中的查询优化
from sqlalchemy.orm import joinedload, selectinload

class CRUDScratchProduct(CRUDBase[ScratchProduct, ScratchProductCreate, ScratchProductUpdate]):
    
    def get_with_relations(
        self, 
        db: Session, 
        id: int
    ) -> Optional[ScratchProduct]:
        """获取项目及其关联数据（优化版）"""
        return db.query(ScratchProduct)\
                 .options(
                     joinedload(ScratchProduct.author),
                     joinedload(ScratchProduct.original_project),
                     joinedload(ScratchProduct.root_project),
                     selectinload(ScratchProduct.adaptations)
                 )\
                 .filter(ScratchProduct.id == id)\
                 .first()
    
    def get_adaptation_chain_optimized(
        self,
        db: Session,
        project_id: int
    ) -> List[ScratchProduct]:
        """优化的改编链查询"""
        # 使用递归CTE查询改编链
        chain_cte = db.execute(text("""
            WITH RECURSIVE adaptation_chain AS (
                -- 基础查询：找到指定项目
                SELECT id, title, author_id, original_project_id, adapt_level, 
                       adaptation_type, created_at, 0 as depth
                FROM scratch_products 
                WHERE id = :project_id
                
                UNION ALL
                
                -- 递归查询：向上查找原始项目
                SELECT p.id, p.title, p.author_id, p.original_project_id, 
                       p.adapt_level, p.adaptation_type, p.created_at, 
                       ac.depth + 1
                FROM scratch_products p
                INNER JOIN adaptation_chain ac ON p.id = ac.original_project_id
                WHERE p.id IS NOT NULL AND ac.depth < 10  -- 防止无限递归
            )
            SELECT * FROM adaptation_chain ORDER BY depth DESC;
        """), {"project_id": project_id})
        
        return chain_cte.fetchall()
    
    def get_popular_projects(
        self,
        db: Session,
        limit: int = 20,
        offset: int = 0
    ) -> List[ScratchProduct]:
        """获取热门项目（基于改编数量）"""
        return db.query(ScratchProduct)\
                 .outerjoin(ScratchProduct.adaptations)\
                 .group_by(ScratchProduct.id)\
                 .order_by(func.count(ScratchProduct.adaptations.property.columns[0]).desc())\
                 .options(joinedload(ScratchProduct.author))\
                 .offset(offset)\
                 .limit(limit)\
                 .all()
```

#### 14.5.2 异步任务处理

```python
# app/tasks/scratch_tasks.py
from celery import Celery
from app.core.config import settings
from app.services.scratch_cache_service import scratch_cache_service
from app.services.scratch_history_service import scratch_history_service
import logging

logger = logging.getLogger(__name__)

# Celery应用实例
celery_app = Celery("scratch_tasks")
celery_app.config_from_object(settings, namespace="CELERY")

@celery_app.task
def update_adaptation_statistics(project_id: int):
    """异步更新改编统计信息"""
    try:
        from app.db.session import SessionLocal
        
        db = SessionLocal()
        try:
            # 重新计算统计信息
            statistics = scratch_history_service._calculate_adaptation_statistics(db, project_id)
            
            # 更新缓存
            cache_key = f"scratch:stats:{project_id}"
            scratch_cache_service.cache.set(cache_key, statistics, ttl=3600)
            
            logger.info(f"Updated statistics for project {project_id}")
            return {"status": "success", "project_id": project_id}
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to update statistics for project {project_id}: {e}")
        return {"status": "error", "error": str(e)}

@celery_app.task
def rebuild_adaptation_tree(root_project_id: int):
    """异步重建改编树"""
    try:
        from app.db.session import SessionLocal
        
        db = SessionLocal()
        try:
            # 重建改编树
            tree = scratch_history_service._build_adaptation_tree(db, root_project_id)
            
            # 更新缓存
            cache_key = f"scratch:tree:{root_project_id}"
            scratch_cache_service.cache.set(cache_key, tree, ttl=7200)
            
            logger.info(f"Rebuilt adaptation tree for project {root_project_id}")
            return {"status": "success", "root_project_id": root_project_id}
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to rebuild tree for project {root_project_id}: {e}")
        return {"status": "error", "error": str(e)}

@celery_app.task
def cleanup_expired_snapshots():
    """清理过期的改编快照"""
    try:
        from app.db.session import SessionLocal
        from app.models.scratch_history import ScratchAdaptationSnapshot
        from datetime import datetime, timedelta
        
        db = SessionLocal()
        try:
            # 删除30天前的快照
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            deleted_count = db.query(ScratchAdaptationSnapshot)\
                             .filter(ScratchAdaptationSnapshot.snapshot_time < cutoff_date)\
                             .delete()
            
            db.commit()
            logger.info(f"Cleaned up {deleted_count} expired snapshots")
            return {"status": "success", "deleted_count": deleted_count}
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to cleanup snapshots: {e}")
        return {"status": "error", "error": str(e)}
```

#### 14.5.3 数据库连接池优化

```python
# app/db/session.py 优化版
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool
from app.core.config import settings

# 优化的数据库引擎配置
engine = create_engine(
    settings.SQLALCHEMY_DATABASE_URI,
    poolclass=QueuePool,
    pool_size=20,           # 连接池大小
    max_overflow=30,        # 最大溢出连接数
    pool_pre_ping=True,     # 连接前检查
    pool_recycle=3600,      # 连接回收时间(1小时)
    echo=settings.DATABASE_ECHO,
    future=True
)

# 监听连接事件，设置查询优化
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """设置数据库连接参数"""
    if "postgresql" in str(dbapi_connection):
        # PostgreSQL优化设置
        cursor = dbapi_connection.cursor()
        cursor.execute("SET statement_timeout = '300s'")  # 查询超时
        cursor.execute("SET lock_timeout = '30s'")        # 锁超时
        cursor.execute("SET idle_in_transaction_session_timeout = '600s'")
        cursor.close()

SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False  # 避免延迟加载问题
)
```

### 14.6 监控和指标

#### 14.6.1 缓存监控

```python
# app/api/endpoints/monitoring.py
from fastapi import APIRouter, Depends
from app.core.redis import redis_manager
from app.api.deps import get_current_superuser

router = APIRouter()

@router.get("/cache/stats")
async def get_cache_stats(current_user = Depends(get_current_superuser)):
    """获取缓存统计信息"""
    try:
        redis_client = redis_manager.client
        info = redis_client.info()
        
        stats = {
            "redis_version": info.get("redis_version"),
            "used_memory": info.get("used_memory_human"),
            "connected_clients": info.get("connected_clients"),
            "total_commands_processed": info.get("total_commands_processed"),
            "keyspace_hits": info.get("keyspace_hits", 0),
            "keyspace_misses": info.get("keyspace_misses", 0),
            "hit_rate": 0
        }
        
        # 计算命中率
        hits = stats["keyspace_hits"]
        misses = stats["keyspace_misses"]
        if hits + misses > 0:
            stats["hit_rate"] = round(hits / (hits + misses) * 100, 2)
        
        return stats
        
    except Exception as e:
        return {"error": str(e)}

@router.get("/cache/keys")
async def get_cache_keys(
    pattern: str = "scratch:*",
    current_user = Depends(get_current_superuser)
):
    """获取缓存键列表"""
    try:
        redis_client = redis_manager.client
        keys = redis_client.keys(pattern)
        
        key_info = []
        for key in keys[:100]:  # 限制返回数量
            ttl = redis_client.ttl(key)
            key_type = redis_client.type(key)
            key_info.append({
                "key": key,
                "ttl": ttl,
                "type": key_type
            })
        
        return {
            "total_keys": len(keys),
            "displayed_keys": len(key_info),
            "keys": key_info
        }
        
    except Exception as e:
        return {"error": str(e)}

@router.delete("/cache/flush")
async def flush_cache(
    pattern: str = "scratch:*",
    current_user = Depends(get_current_superuser)
):
    """清空缓存"""
    try:
        redis_client = redis_manager.client
        keys = redis_client.keys(pattern)
        
        if keys:
            deleted_count = redis_client.delete(*keys)
            return {"message": f"Deleted {deleted_count} cache keys"}
        else:
            return {"message": "No keys found to delete"}
            
    except Exception as e:
        return {"error": str(e)}
```

---

## 任务15：编写API文档和使
用示例

### 15.1 API文档结构

#### 15.1.1 OpenAPI文档配置

```python
# app/main.py 中的文档配置
from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi

app = FastAPI(
    title="Scratch项目改编API",
    description="支持Scratch项目改编功能的完整API文档",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title="Scratch项目改编API",
        version="1.0.0",
        description="""
        ## 功能概述
        
        本API提供完整的Scratch项目改编功能，支持：
        
        - 🎯 **项目管理**: 创建、更新、删除、查询Scratch项目
        - 🔄 **改编功能**: 基于现有项目创建改编版本
        - 🌳 **关系追踪**: 维护和查询项目改编关系链
        - 📊 **统计分析**: 提供改编数据统计和分析
        - 🔐 **权限控制**: 基于角色的访问控制
        - ⚡ **性能优化**: 缓存和异步处理支持
        
        ## 改编类型
        
        - **REMIX**: 重新混合 - 基于原项目进行创意改编
        - **EXTENSION**: 扩展 - 在原项目基础上添加新功能
        - **MODIFICATION**: 修改 - 对原项目进行局部调整
        - **TRANSLATION**: 翻译 - 将项目翻译为其他语言
        - **EDUCATIONAL**: 教育版 - 为教学目的简化或改编
        
        ## 认证方式
        
        使用Bearer Token进行身份验证：
        ```
        Authorization: Bearer <your_token>
        ```
        """,
        routes=app.routes,
    )
    
    # 添加标签描述
    openapi_schema["tags"] = [
        {
            "name": "projects",
            "description": "项目基础操作 - 创建、查询、更新、删除项目"
        },
        {
            "name": "adaptations", 
            "description": "改编功能 - 创建改编、查询改编关系、改编统计"
        },
        {
            "name": "history",
            "description": "历史追踪 - 改编历史记录、活动日志、快照管理"
        },
        {
            "name": "search",
            "description": "搜索功能 - 项目搜索、筛选、排序"
        },
        {
            "name": "admin",
            "description": "管理功能 - 系统管理、缓存管理、监控"
        }
    ]
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi
```

#### 15.1.2 详细API端点文档

```python
# app/api/endpoints/scratch.py 中的文档增强
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from typing import List, Optional, Dict, Any

router = APIRouter()

@router.post(
    "/projects",
    response_model=ScratchProductResponse,
    status_code=201,
    summary="创建新项目",
    description="""
    创建一个新的Scratch项目。
    
    **请求参数说明：**
    - `title`: 项目标题（必填，1-100字符）
    - `description`: 项目描述（可选，最多1000字符）
    - `content`: 项目内容JSON数据
    - `tags`: 项目标签列表
    - `is_public`: 是否公开项目（默认false）
    
    **权限要求：** 需要SCRATCH_PROJECT:CREATE权限
    
    **示例请求：**
    ```json
    {
        "title": "我的第一个Scratch项目",
        "description": "这是一个简单的动画项目",
        "content": {"sprites": [], "scripts": []},
        "tags": ["动画", "初学者"],
        "is_public": true
    }
    ```
    
    **返回数据：** 创建成功的项目完整信息
    """,
    tags=["projects"]
)
async def create_project(
    project_in: ScratchProductCreate = Body(..., example={
        "title": "我的第一个Scratch项目",
        "description": "这是一个简单的动画项目",
        "content": {"sprites": [], "scripts": []},
        "tags": ["动画", "初学者"],
        "is_public": True
    }),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 实现代码...
    pass

@router.post(
    "/projects/{project_id}/adapt",
    response_model=ScratchProductResponse,
    status_code=201,
    summary="创建项目改编",
    description="""
    基于现有项目创建改编版本。
    
    **改编流程：**
    1. 验证原项目存在且有访问权限
    2. 检查改编层级限制（最多5层）
    3. 验证无循环引用
    4. 复制项目元数据
    5. 建立改编关系
    6. 记录改编历史
    
    **改编类型说明：**
    - `REMIX`: 重新混合，适合创意改编
    - `EXTENSION`: 扩展功能，在原基础上添加
    - `MODIFICATION`: 局部修改，调整部分内容
    - `TRANSLATION`: 语言翻译版本
    - `EDUCATIONAL`: 教育简化版本
    
    **权限要求：** 
    - 原项目：READ权限
    - 新项目：CREATE权限
    
    **限制条件：**
    - 改编层级不超过5层
    - 不能改编自己的项目
    - 不能创建循环改编关系
    """,
    tags=["adaptations"]
)
async def create_adaptation(
    project_id: int = Path(..., description="要改编的原项目ID"),
    adaptation_data: ScratchProductAdapt = Body(..., example={
        "title": "改编版：我的动画项目",
        "description": "基于原项目的改进版本",
        "adaptation_type": "REMIX",
        "content": {"sprites": [], "scripts": []},
        "tags": ["改编", "动画", "创意"]
    }),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 实现代码...
    pass

@router.get(
    "/projects/{project_id}/chain",
    response_model=List[AdaptationChainNode],
    summary="获取改编链",
    description="""
    获取指定项目的完整改编链，从根项目到当前项目的完整路径。
    
    **改编链说明：**
    - 按改编层级从低到高排序
    - 包含每个节点的基础信息
    - 显示改编类型和创建时间
    - 支持缓存加速查询
    
    **返回数据结构：**
    ```json
    [
        {
            "project_id": 1,
            "title": "原始项目",
            "author_name": "原作者",
            "created_at": "2024-01-01T00:00:00Z",
            "adaptation_type": null,
            "level": 0
        },
        {
            "project_id": 2,
            "title": "第一层改编",
            "author_name": "改编者1",
            "created_at": "2024-01-02T00:00:00Z", 
            "adaptation_type": "REMIX",
            "level": 1
        }
    ]
    ```
    """,
    tags=["adaptations"]
)
async def get_adaptation_chain(
    project_id: int = Path(..., description="项目ID"),
    use_cache: bool = Query(True, description="是否使用缓存"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 实现代码...
    pass
```

### 15.2 使用示例和教程

#### 15.2.1 基础使用流程

```python
# examples/basic_usage.py
"""
Scratch项目改编API基础使用示例
"""

import requests
import json
from typing import Dict, Any

class ScratchAdaptationClient:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
    
    def create_project(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建新项目"""
        response = requests.post(
            f"{self.base_url}/api/v1/scratch/projects",
            headers=self.headers,
            json=project_data
        )
        response.raise_for_status()
        return response.json()
    
    def create_adaptation(self, original_id: int, adaptation_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建改编项目"""
        response = requests.post(
            f"{self.base_url}/api/v1/scratch/projects/{original_id}/adapt",
            headers=self.headers,
            json=adaptation_data
        )
        response.raise_for_status()
        return response.json()
    
    def get_adaptation_chain(self, project_id: int) -> List[Dict[str, Any]]:
        """获取改编链"""
        response = requests.get(
            f"{self.base_url}/api/v1/scratch/projects/{project_id}/chain",
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()
    
    def get_adaptation_tree(self, project_id: int) -> Dict[str, Any]:
        """获取改编树"""
        response = requests.get(
            f"{self.base_url}/api/v1/scratch/projects/{project_id}/tree",
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()

# 使用示例
def main():
    # 初始化客户端
    client = ScratchAdaptationClient(
        base_url="http://localhost:8000",
        token="your_access_token_here"
    )
    
    # 1. 创建原始项目
    print("1. 创建原始项目...")
    original_project = client.create_project({
        "title": "原始动画项目",
        "description": "一个简单的角色动画",
        "content": {
            "sprites": [
                {
                    "name": "Cat",
                    "x": 0,
                    "y": 0,
                    "scripts": [
                        {
                            "blocks": [
                                {"type": "event_whenflagclicked"},
                                {"type": "motion_move", "inputs": {"STEPS": 10}},
                                {"type": "looks_say", "inputs": {"MESSAGE": "Hello!"}}
                            ]
                        }
                    ]
                }
            ]
        },
        "tags": ["动画", "初学者"],
        "is_public": True
    })
    
    original_id = original_project["id"]
    print(f"创建成功，项目ID: {original_id}")
    
    # 2. 创建第一层改编
    print("\n2. 创建第一层改编...")
    first_adaptation = client.create_adaptation(original_id, {
        "title": "改编版：会跳舞的猫",
        "description": "在原项目基础上添加跳舞动作",
        "adaptation_type": "EXTENSION",
        "content": {
            "sprites": [
                {
                    "name": "Cat",
                    "x": 0,
                    "y": 0,
                    "scripts": [
                        {
                            "blocks": [
                                {"type": "event_whenflagclicked"},
                                {"type": "motion_move", "inputs": {"STEPS": 10}},
                                {"type": "motion_turn_right", "inputs": {"DEGREES": 15}},
                                {"type": "motion_move", "inputs": {"STEPS": 10}},
                                {"type": "looks_say", "inputs": {"MESSAGE": "I can dance!"}}
                            ]
                        }
                    ]
                }
            ]
        },
        "tags": ["改编", "动画", "跳舞"]
    })
    
    first_adaptation_id = first_adaptation["id"]
    print(f"第一层改编创建成功，项目ID: {first_adaptation_id}")
    
    # 3. 创建第二层改编
    print("\n3. 创建第二层改编...")
    second_adaptation = client.create_adaptation(first_adaptation_id, {
        "title": "终极版：会唱歌跳舞的猫",
        "description": "添加音乐和更复杂的动作",
        "adaptation_type": "REMIX",
        "content": {
            "sprites": [
                {
                    "name": "Cat",
                    "x": 0,
                    "y": 0,
                    "scripts": [
                        {
                            "blocks": [
                                {"type": "event_whenflagclicked"},
                                {"type": "sound_play", "inputs": {"SOUND": "meow"}},
                                {"type": "motion_move", "inputs": {"STEPS": 10}},
                                {"type": "motion_turn_right", "inputs": {"DEGREES": 15}},
                                {"type": "motion_move", "inputs": {"STEPS": 10}},
                                {"type": "looks_say", "inputs": {"MESSAGE": "Meow! I love music!"}}
                            ]
                        }
                    ]
                }
            ]
        },
        "tags": ["改编", "动画", "音乐", "高级"]
    })
    
    second_adaptation_id = second_adaptation["id"]
    print(f"第二层改编创建成功，项目ID: {second_adaptation_id}")
    
    # 4. 查看改编链
    print("\n4. 查看完整改编
链...") 
    chain = client.get_adaptation_chain(second_adaptation_id)
    print("改编链结构:")
    for i, node in enumerate(chain):
        indent = "  " * i
        print(f"{indent}├─ {node['title']} (ID: {node['project_id']}, 作者: {node['author_name']})")
        if node['adaptation_type']:
            print(f"{indent}   类型: {node['adaptation_type']}, 层级: {node['level']}")
    
    # 5. 查看改编树
    print("\n5. 查看改编树...")
    tree = client.get_adaptation_tree(original_id)
    print("改编树结构:")
    print_tree(tree, 0)

def print_tree(node, depth):
    """递归打印改编树"""
    indent = "  " * depth
    print(f"{indent}├─ {node['title']} (ID: {node['project_id']})")
    if node.get('adaptation_type'):
        print(f"{indent}   类型: {node['adaptation_type']}, 层级: {node['level']}")
    
    for child in node.get('children', []):
        print_tree(child, depth + 1)

if __name__ == "__main__":
    main()
```

#### 15.2.2 高级功能示例

```python
# examples/advanced_usage.py
"""
Scratch项目改编API高级功能示例
"""

import asyncio
import aiohttp
from typing import List, Dict, Any

class AsyncScratchClient:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
    
    async def batch_create_adaptations(
        self, 
        original_id: int, 
        adaptations: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """批量创建改编项目"""
        async with aiohttp.ClientSession() as session:
            tasks = []
            for adaptation_data in adaptations:
                task = self._create_single_adaptation(
                    session, original_id, adaptation_data
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            return [r for r in results if not isinstance(r, Exception)]
    
    async def _create_single_adaptation(
        self, 
        session: aiohttp.ClientSession,
        original_id: int, 
        adaptation_data: Dict[str, Any]
    ):
        """创建单个改编项目"""
        url = f"{self.base_url}/api/v1/scratch/projects/{original_id}/adapt"
        async with session.post(url, headers=self.headers, json=adaptation_data) as response:
            response.raise_for_status()
            return await response.json()
    
    async def search_projects(
        self, 
        query: str = "",
        adaptation_type: str = None,
        min_adaptations: int = None,
        tags: List[str] = None,
        page: int = 1,
        limit: int = 20
    ) -> Dict[str, Any]:
        """高级项目搜索"""
        params = {
            'q': query,
            'page': page,
            'limit': limit
        }
        
        if adaptation_type:
            params['adaptation_type'] = adaptation_type
        if min_adaptations is not None:
            params['min_adaptations'] = min_adaptations
        if tags:
            params['tags'] = ','.join(tags)
        
        async with aiohttp.ClientSession() as session:
            url = f"{self.base_url}/api/v1/scratch/projects/search"
            async with session.get(url, headers=self.headers, params=params) as response:
                response.raise_for_status()
                return await response.json()

# 高级使用示例
async def advanced_demo():
    client = AsyncScratchClient(
        base_url="http://localhost:8000",
        token="your_access_token_here"
    )
    
    # 1. 批量创建改编项目
    print("1. 批量创建改编项目...")
    original_id = 1  # 假设已有原始项目
    
    adaptations = [
        {
            "title": "教育版：基础动画",
            "description": "适合小学生学习的简化版本",
            "adaptation_type": "EDUCATIONAL",
            "content": {"sprites": [], "scripts": []},
            "tags": ["教育", "简化"]
        },
        {
            "title": "中文版：动画项目",
            "description": "翻译为中文的版本",
            "adaptation_type": "TRANSLATION", 
            "content": {"sprites": [], "scripts": []},
            "tags": ["中文", "翻译"]
        },
        {
            "title": "增强版：高级动画",
            "description": "添加高级特效的版本",
            "adaptation_type": "EXTENSION",
            "content": {"sprites": [], "scripts": []},
            "tags": ["高级", "特效"]
        }
    ]
    
    results = await client.batch_create_adaptations(original_id, adaptations)
    print(f"批量创建完成，成功创建 {len(results)} 个改编项目")
    
    # 2. 高级搜索示例
    print("\n2. 搜索所有教育类改编项目...")
    search_results = await client.search_projects(
        query="教育",
        adaptation_type="EDUCATIONAL",
        tags=["教育", "简化"],
        limit=10
    )
    
    print(f"找到 {search_results['total']} 个教育类项目:")
    for project in search_results['items']:
        print(f"  - {project['title']} (改编类型: {project.get('adaptation_type', 'N/A')})")

if __name__ == "__main__":
    asyncio.run(advanced_demo())
```

#### 15.2.3 错误处理示例

```python
# examples/error_handling.py
"""
Scratch项目改编API错误处理示例
"""

import requests
from requests.exceptions import HTTPError, ConnectionError, Timeout
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ScratchAPIError(Exception):
    """自定义API错误类"""
    def __init__(self, message: str, status_code: int = None, error_details: dict = None):
        super().__init__(message)
        self.status_code = status_code
        self.error_details = error_details or {}

class RobustScratchClient:
    def __init__(self, base_url: str, token: str, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        self.timeout = timeout
    
    def _handle_response(self, response: requests.Response):
        """统一处理API响应"""
        try:
            response.raise_for_status()
            return response.json()
        except HTTPError as e:
            error_data = {}
            try:
                error_data = response.json()
            except:
                error_data = {"message": response.text}
            
            if response.status_code == 400:
                raise ScratchAPIError(
                    f"请求参数错误: {error_data.get('detail', '未知错误')}",
                    status_code=400,
                    error_details=error_data
                )
            elif response.status_code == 401:
                raise ScratchAPIError("认证失败，请检查访问令牌", status_code=401)
            elif response.status_code == 403:
                raise ScratchAPIError(
                    f"权限不足: {error_data.get('detail', '无权限执行此操作')}",
                    status_code=403
                )
            elif response.status_code == 404:
                raise ScratchAPIError("资源不存在", status_code=404)
            elif response.status_code == 409:
                raise ScratchAPIError(
                    f"操作冲突: {error_data.get('detail', '资源冲突')}",
                    status_code=409,
                    error_details=error_data
                )
            elif response.status_code >= 500:
                raise ScratchAPIError("服务器内部错误", status_code=response.status_code)
            else:
                raise ScratchAPIError(f"未知错误: {e}", status_code=response.status_code)
    
    def create_adaptation_safe(self, original_id: int, adaptation_data: dict):
        """安全的改编创建方法"""
        try:
            logger.info(f"尝试创建项目 {original_id} 的改编...")
            
            response = requests.post(
                f"{self.base_url}/api/v1/scratch/projects/{original_id}/adapt",
                headers=self.headers,
                json=adaptation_data,
                timeout=self.timeout
            )
            
            result = self._handle_response(response)
            logger.info(f"改编创建成功，新项目ID: {result['id']}")
            return result
            
        except ScratchAPIError as e:
            logger.error(f"API错误: {e}")
            if e.status_code == 409:
                # 处理具体的冲突错误
                error_details = e.error_details
                if "circular_reference" in str(error_details):
                    logger.error("检测到循环引用，无法创建改编")
                elif "max_depth" in str(error_details):
                    logger.error("改编层级已达上限，无法继续改编")
                elif "self_adaptation" in str(error_details):
                    logger.error("不能改编自己的项目")
            raise
            
        except ConnectionError:
            logger.error("网络连接错误，请检查网络状态")
            raise ScratchAPIError("网络连接失败")
            
        except Timeout:
            logger.error("请求超时")
            raise ScratchAPIError("请求超时，请稍后重试")
            
        except Exception as e:
            logger.error(f"未预期的错误: {e}")
            raise ScratchAPIError(f"未知错误: {e}")

# 错误处理示例
def error_handling_demo():
    client = RobustScratchClient(
        base_url="http://localhost:8000",
        token="your_access_token_here"
    )
    
    # 测试各种错误情况
    test_cases = [
        {
            "name": "正常创建",
            "original_id": 1,
            "data": {
                "title": "正常改编项目",
                "description": "这应该成功创建",
                "adaptation_type": "REMIX"
            }
        },
        {
            "name": "循环引用错误",
            "original_id": 2,  # 假设项目2是项目1的改编
            "data": {
                "title": "循环改编",
                "description": "这会导致循环引用",
                "adaptation_type": "REMIX"
            }
        },
        {
            "name": "不存在的项目",
            "original_id": 99999,
            "data": {
                "title": "基于不存在项目的改编",
                "description": "这会返回404错误",
                "adaptation_type": "REMIX"
            }
        },
        {
            "name": "无效的改编类型",
            "original_id": 1,
            "data": {
                "title": "无效改编类型",
                "description": "使用无效的改编类型",
                "adaptation_type": "INVALID_TYPE"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试用例: {test_case['name']}")
        try:
            result = client.create_adaptation_safe(
                test_case['original_id'],
                test_case['data']
            )
            print(f"✅ 成功: 创建了项目 {result['id']}")
            
        except ScratchAPIError as e:
            print(f"❌ API错误 (状态码 {e.status_code}): {e}")
            if e.error_details:
                print(f"   详细信息: {e.error_details}")
        
        except Exception as e:
            print(f"❌ 未知错误: {e}")

if __name__ == "__main__":
    error_handling_demo()
```

### 15.3 测试用例和验证

#### 15.3.1 单元测试

```python
# tests/test_scratch_adaptation.py
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from app.main import app
from app.models.scratch import ScratchProduct
from app.models.user import User
from app.schemas.scratch import AdaptationType
from tests.utils import create_test_user, create_test_project

client = TestClient(app)

class TestScratchAdaptation:
    """Scratch改编功能测试套件"""
    
    @pytest.fixture
    def test_user(self, db: Session):
        """创建测试用户"""
        return create_test_user(db, "testuser", "<EMAIL>")
    
    @pytest.fixture
    def test_project(self, db: Session, test_user: User):
        """创建测试项目"""
        return create_test_project(db, test_user.id, "测试项目")
    
    def test_create_project(self, test_user: User):
        """测试创建项目"""
        project_data = {
            "title": "新测试项目",
            "description": "这是一个测试项目",
            "content": {"sprites": [], "scripts": []},
            "tags": ["测试"],
            "is_public": True
        }
        
        response = client.post(
            "/api/v1/scratch/projects",
            json=project_data,
            headers={"Authorization": f"Bearer {test_user.token}"}
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == project_data["title"]
        assert data["author_id"] == test_user.id
        assert data["original_project_id"] is None
        assert data["adapt_level"] == 0
    
    def test_create_adaptation_success(self, test_user: User, test_project: ScratchProduct):
        """测试成功创建改编"""
        adaptation_data = {
            "title": "改编项目",
            "description": "基于测试项目的改编",
            "adaptation_type": "REMIX",
            "content": {"sprites": [], "scripts":
 []},
            "tags": ["改编", "测试"]
        }
        
        response = client.post(
            f"/api/v1/scratch/projects/{test_project.id}/adapt",
            json=adaptation_data,
            headers={"Authorization": f"Bearer {test_user.token}"}
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == adaptation_data["title"]
        assert data["original_project_id"] == test_project.id
        assert data["adapt_level"] == 1
        assert data["adaptation_type"] == "REMIX"
    
    def test_create_adaptation_circular_reference(self, test_user: User, test_project: ScratchProduct):
        """测试循环引用检测"""
        # 先创建一个改编
        adaptation_data = {
            "title": "第一层改编",
            "adaptation_type": "REMIX",
            "content": {"sprites": [], "scripts": []}
        }
        
        response = client.post(
            f"/api/v1/scratch/projects/{test_project.id}/adapt",
            json=adaptation_data,
            headers={"Authorization": f"Bearer {test_user.token}"}
        )
        
        assert response.status_code == 201
        first_adaptation_id = response.json()["id"]
        
        # 尝试让原项目改编这个改编项目（应该失败）
        circular_data = {
            "title": "循环改编",
            "adaptation_type": "REMIX",
            "content": {"sprites": [], "scripts": []}
        }
        
        response = client.post(
            f"/api/v1/scratch/projects/{first_adaptation_id}/adapt",
            json=circular_data,
            headers={"Authorization": f"Bearer {test_user.token}"}
        )
        
        assert response.status_code == 409
        assert "circular reference" in response.json()["detail"].lower()
    
    def test_adaptation_chain(self, test_user: User, test_project: ScratchProduct):
        """测试改编链查询"""
        response = client.get(
            f"/api/v1/scratch/projects/{test_project.id}/chain",
            headers={"Authorization": f"Bearer {test_user.token}"}
        )
        
        assert response.status_code == 200
        chain = response.json()
        assert len(chain) == 1
        assert chain[0]["project_id"] == test_project.id
        assert chain[0]["level"] == 0
    
    def test_adaptation_tree(self, test_user: User, test_project: ScratchProduct):
        """测试改编树查询"""
        response = client.get(
            f"/api/v1/scratch/projects/{test_project.id}/tree",
            headers={"Authorization": f"Bearer {test_user.token}"}
        )
        
        assert response.status_code == 200
        tree = response.json()
        assert tree["project_id"] == test_project.id
        assert tree["level"] == 0
        assert isinstance(tree["children"], list)
    
    def test_max_adaptation_depth(self, test_user: User, test_project: ScratchProduct):
        """测试最大改编深度限制"""
        current_project_id = test_project.id
        
        # 创建5层改编（达到上限）
        for i in range(5):
            adaptation_data = {
                "title": f"第{i+1}层改编",
                "adaptation_type": "REMIX",
                "content": {"sprites": [], "scripts": []}
            }
            
            response = client.post(
                f"/api/v1/scratch/projects/{current_project_id}/adapt",
                json=adaptation_data,
                headers={"Authorization": f"Bearer {test_user.token}"}
            )
            
            assert response.status_code == 201
            current_project_id = response.json()["id"]
        
        # 尝试创建第6层改编（应该失败）
        sixth_layer_data = {
            "title": "第6层改编",
            "adaptation_type": "REMIX",
            "content": {"sprites": [], "scripts": []}
        }
        
        response = client.post(
            f"/api/v1/scratch/projects/{current_project_id}/adapt",
            json=sixth_layer_data,
            headers={"Authorization": f"Bearer {test_user.token}"}
        )
        
        assert response.status_code == 409
        assert "max depth" in response.json()["detail"].lower()

    @pytest.mark.asyncio
    async def test_adaptation_history_recording(self, test_user: User, test_project: ScratchProduct):
        """测试改编历史记录"""
        # 创建改编
        adaptation_data = {
            "title": "历史测试改编",
            "adaptation_type": "REMIX",
            "content": {"sprites": [], "scripts": []}
        }
        
        response = client.post(
            f"/api/v1/scratch/projects/{test_project.id}/adapt",
            json=adaptation_data,
            headers={"Authorization": f"Bearer {test_user.token}"}
        )
        
        assert response.status_code == 201
        adaptation_id = response.json()["id"]
        
        # 检查历史记录
        response = client.get(
            f"/api/v1/scratch/projects/{adaptation_id}/history",
            headers={"Authorization": f"Bearer {test_user.token}"}
        )
        
        assert response.status_code == 200
        history = response.json()
        assert len(history) >= 1
        
        # 检查改编记录
        adapt_records = [h for h in history if h["action_type"] == "ADAPT"]
        assert len(adapt_records) >= 1
        assert adapt_records[0]["original_project_id"] == test_project.id
```

#### 15.3.2 集成测试

```python
# tests/test_integration.py
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from app.main import app
from tests.utils import create_test_user, create_test_project

client = TestClient(app)

class TestScratchIntegration:
    """集成测试套件"""
    
    def test_complete_adaptation_workflow(self, db: Session):
        """测试完整的改编工作流程"""
        # 1. 创建用户
        user1 = create_test_user(db, "creator", "<EMAIL>")
        user2 = create_test_user(db, "adapter", "<EMAIL>")
        
        # 2. 用户1创建原始项目
        original_data = {
            "title": "原创动画项目",
            "description": "一个创新的动画项目",
            "content": {
                "sprites": [{"name": "Cat", "x": 0, "y": 0}],
                "scripts": []
            },
            "tags": ["原创", "动画"],
            "is_public": True
        }
        
        response = client.post(
            "/api/v1/scratch/projects",
            json=original_data,
            headers={"Authorization": f"Bearer {user1.token}"}
        )
        
        assert response.status_code == 201
        original_project = response.json()
        
        # 3. 用户2创建改编
        adaptation_data = {
            "title": "改编：增强版动画",
            "description": "在原项目基础上添加音效",
            "adaptation_type": "EXTENSION",
            "content": {
                "sprites": [{"name": "Cat", "x": 0, "y": 0}],
                "scripts": [{"type": "sound_play"}]
            },
            "tags": ["改编", "音效", "增强"]
        }
        
        response = client.post(
            f"/api/v1/scratch/projects/{original_project['id']}/adapt",
            json=adaptation_data,
            headers={"Authorization": f"Bearer {user2.token}"}
        )
        
        assert response.status_code == 201
        adaptation = response.json()
        
        # 4. 验证改编关系
        assert adaptation["original_project_id"] == original_project["id"]
        assert adaptation["root_project_id"] == original_project["id"]
        assert adaptation["adapt_level"] == 1
        assert adaptation["adaptation_type"] == "EXTENSION"
        
        # 5. 查询改编链
        response = client.get(
            f"/api/v1/scratch/projects/{adaptation['id']}/chain",
            headers={"Authorization": f"Bearer {user2.token}"}
        )
        
        assert response.status_code == 200
        chain = response.json()
        assert len(chain) == 2
        assert chain[0]["project_id"] == original_project["id"]
        assert chain[1]["project_id"] == adaptation["id"]
        
        # 6. 查询改编树
        response = client.get(
            f"/api/v1/scratch/projects/{original_project['id']}/tree",
            headers={"Authorization": f"Bearer {user1.token}"}
        )
        
        assert response.status_code == 200
        tree = response.json()
        assert tree["project_id"] == original_project["id"]
        assert len(tree["children"]) == 1
        assert tree["children"][0]["project_id"] == adaptation["id"]
        
        # 7. 查询统计信息
        response = client.get(
            f"/api/v1/scratch/projects/{original_project['id']}/stats",
            headers={"Authorization": f"Bearer {user1.token}"}
        )
        
        assert response.status_code == 200
        stats = response.json()
        assert stats["total_adaptations"] == 1
        assert stats["direct_adaptations"] == 1
        
        # 8. 搜索改编项目
        response = client.get(
            "/api/v1/scratch/projects/search",
            params={"q": "改编", "adaptation_type": "EXTENSION"},
            headers={"Authorization": f"Bearer {user2.token}"}
        )
        
        assert response.status_code == 200
        search_results = response.json()
        assert search_results["total"] >= 1
        found_adaptation = next(
            (item for item in search_results["items"] if item["id"] == adaptation["id"]),
            None
        )
        assert found_adaptation is not None
```

### 15.4 部署指南

#### 15.4.1 环境配置

```yaml
# docker-compose.production.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.production
    environment:
      - DATABASE_URL=****************************************/scratch_db
      - REDIS_URL=redis://redis-master:6379/0
      - CELERY_BROKER_URL=redis://redis-master:6379/1
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
    depends_on:
      - postgres
      - redis-master
      - redis-sentinel
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=scratch_db
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    
  redis-master:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    
  redis-sentinel:
    image: redis:7-alpine
    command: redis-sentinel /etc/redis/sentinel.conf
    volumes:
      - ./config/sentinel.conf:/etc/redis/sentinel.conf
    depends_on:
      - redis-master
    restart: unless-stopped
    
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.production
    command: celery -A app.tasks.scratch_tasks worker --loglevel=info
    environment:
      - DATABASE_URL=****************************************/scratch_db
      - REDIS_URL=redis://redis-master:6379/0
      - CELERY_BROKER_URL=redis://redis-master:6379/1
    depends_on:
      - postgres
      - redis-master
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.production
    command: celery -A app.tasks.scratch_tasks beat --loglevel=info
    environment:
      - DATABASE_URL=****************************************/scratch_db
      - REDIS_URL=redis://redis-master:6379/0
      - CELERY_BROKER_URL=redis://redis-master:6379/1
    depends_on:
      - postgres
      - redis-master
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

#### 15.4.2 生产环境配置

```python
# app/core/config.py (生产环境部分)
import secrets
from typing import Optional, Dict, Any
from pydantic import BaseSettings, validator

class Settings(BaseSettings):
    # 基础配置
    PROJECT_NAME: str = "Scratch Adaptation API"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    
    # 环境配置
    ENVIRONMENT: str = "development"
    DEBUG: bool = False
    
    # 数据库配置
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = ""
    POSTGRES_DB: str = "scratch_db"
    POSTGRES_PORT: str = "5432"
    SQLALCHEMY_DATABASE_URI: Optional[str] = None
    
    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_TIMEOUT: int = 5
    REDIS_SENTINEL_ENABLED: bool = False
    REDIS_SENTINEL_HOST: str = "localhost"
    REDIS_SENTINEL_PORT: int = 26379
    REDIS_MASTER_NAME: str = "mymaster"
    
    # Celery配置
    CELERY_BROKER_
URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/1"
    CELERY_TASK_SERIALIZER: str = "json"
    CELERY_RESULT_SERIALIZER: str = "json"
    CELERY_ACCEPT_CONTENT: list = ["json"]
    CELERY_TIMEZONE: str = "UTC"
    CELERY_ENABLE_UTC: bool = True
    
    # 安全配置
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 30  # 30 days
    ALGORITHM: str = "HS256"
    
    # CORS配置
    BACKEND_CORS_ORIGINS: list = ["http://localhost:3000", "https://yourdomain.com"]
    
    # 改编功能配置
    MAX_ADAPTATION_DEPTH: int = 5
    ADAPTATION_CACHE_TTL: int = 3600
    HISTORY_RETENTION_DAYS: int = 365
    
    # 文件上传配置
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: set = {".sb3", ".json", ".png", ".jpg", ".jpeg", ".gif"}
    UPLOAD_PATH: str = "./uploads"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE: str = "./logs/app.log"
    
    # 性能配置
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 30
    DATABASE_POOL_TIMEOUT: int = 30
    DATABASE_POOL_RECYCLE: int = 3600
    
    # 监控配置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    HEALTH_CHECK_INTERVAL: int = 30
    
    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return f"postgresql://{values.get('POSTGRES_USER')}:{values.get('POSTGRES_PASSWORD')}@{values.get('POSTGRES_SERVER')}:{values.get('POSTGRES_PORT')}/{values.get('POSTGRES_DB')}"
    
    @validator("ENVIRONMENT")
    def validate_environment(cls, v):
        if v not in ["development", "testing", "production"]:
            raise ValueError("Environment must be development, testing, or production")
        return v
    
    class Config:
        case_sensitive = True
        env_file = ".env"

# 生产环境特殊配置
class ProductionSettings(Settings):
    DEBUG: bool = False
    LOG_LEVEL: str = "WARNING"
    ENVIRONMENT: str = "production"
    
    # 生产环境安全配置
    SECURE_COOKIES: bool = True
    HTTPS_ONLY: bool = True
    HSTS_MAX_AGE: int = 31536000
    
    # 生产环境性能配置
    DATABASE_POOL_SIZE: int = 50
    DATABASE_MAX_OVERFLOW: int = 100
    REDIS_TIMEOUT: int = 10
    
    # 生产环境缓存配置
    ADAPTATION_CACHE_TTL: int = 7200  # 2小时
    ENABLE_REDIS_CLUSTER: bool = True

settings = ProductionSettings() if os.getenv("ENVIRONMENT") == "production" else Settings()
```

#### 15.4.3 部署脚本

```bash
#!/bin/bash
# deploy.sh - 生产环境部署脚本

set -e

echo "🚀 开始部署 Scratch 改编功能..."

# 1. 检查环境变量
if [ -z "$SECRET_KEY" ]; then
    echo "❌ 错误: SECRET_KEY 环境变量未设置"
    exit 1
fi

if [ -z "$DATABASE_URL" ]; then
    echo "❌ 错误: DATABASE_URL 环境变量未设置"
    exit 1
fi

# 2. 拉取最新代码
echo "📥 拉取最新代码..."
git pull origin main

# 3. 构建 Docker 镜像
echo "🔨 构建 Docker 镜像..."
docker-compose -f docker-compose.production.yml build --no-cache

# 4. 停止旧服务
echo "⏹️ 停止旧服务..."
docker-compose -f docker-compose.production.yml down

# 5. 启动数据库和 Redis
echo "🗄️ 启动数据库和 Redis..."
docker-compose -f docker-compose.production.yml up -d postgres redis-master redis-sentinel

# 等待服务启动
sleep 10

# 6. 运行数据库迁移
echo "🔄 运行数据库迁移..."
docker-compose -f docker-compose.production.yml run --rm app alembic upgrade head

# 7. 启动所有服务
echo "🚀 启动所有服务..."
docker-compose -f docker-compose.production.yml up -d

# 8. 健康检查
echo "🏥 执行健康检查..."
sleep 30

# 检查 API 服务
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ API 服务健康检查通过"
else
    echo "❌ API 服务健康检查失败"
    exit 1
fi

# 检查 Redis 服务
if docker-compose -f docker-compose.production.yml exec redis-master redis-cli ping | grep -q PONG; then
    echo "✅ Redis 服务健康检查通过"
else
    echo "❌ Redis 服务健康检查失败"
    exit 1
fi

# 检查数据库服务
if docker-compose -f docker-compose.production.yml exec postgres pg_isready -U user -d scratch_db > /dev/null; then
    echo "✅ 数据库服务健康检查通过"
else
    echo "❌ 数据库服务健康检查失败"
    exit 1
fi

# 9. 运行基本功能测试
echo "🧪 运行基本功能测试..."
docker-compose -f docker-compose.production.yml run --rm app pytest tests/test_health.py -v

echo "🎉 部署完成！"
echo "📊 服务状态:"
docker-compose -f docker-compose.production.yml ps

echo "📖 查看日志:"
echo "  docker-compose -f docker-compose.production.yml logs -f app"
echo "  docker-compose -f docker-compose.production.yml logs -f celery-worker"

echo "🌐 API 文档: http://localhost:8000/docs"
echo "📈 监控面板: http://localhost:9090"
```

#### 15.4.4 监控和日志

```python
# app/core/logging.py
import logging
import sys
from pathlib import Path
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from app.core.config import settings

def setup_logging():
    """配置日志系统"""
    
    # 创建日志目录
    log_dir = Path(settings.LOG_FILE).parent
    log_dir.mkdir(exist_ok=True)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
    
    # 清除默认处理器
    root_logger.handlers.clear()
    
    # 创建格式化器
    formatter = logging.Formatter(settings.LOG_FORMAT)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    
    # 文件处理器 - 按大小轮转
    file_handler = RotatingFileHandler(
        settings.LOG_FILE,
        maxBytes=50*1024*1024,  # 50MB
        backupCount=5
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
    root_logger.addHandler(file_handler)
    
    # 错误日志处理器 - 按时间轮转
    error_log_file = log_dir / "error.log"
    error_handler = TimedRotatingFileHandler(
        error_log_file,
        when="midnight",
        interval=1,
        backupCount=30
    )
    error_handler.setFormatter(formatter)
    error_handler.setLevel(logging.ERROR)
    root_logger.addHandler(error_handler)
    
    # 改编功能专用日志器
    adaptation_logger = logging.getLogger("scratch.adaptation")
    adaptation_log_file = log_dir / "adaptation.log"
    adaptation_handler = TimedRotatingFileHandler(
        adaptation_log_file,
        when="midnight",
        interval=1,
        backupCount=7
    )
    adaptation_handler.setFormatter(formatter)
    adaptation_logger.addHandler(adaptation_handler)
    adaptation_logger.setLevel(logging.INFO)
    
    logging.info("日志系统初始化完成")

# 性能监控
class PerformanceMonitor:
    """性能监控工具"""
    
    def __init__(self):
        self.metrics = {}
        self.logger = logging.getLogger("performance")
    
    def record_api_call(self, endpoint: str, duration: float, status_code: int):
        """记录 API 调用指标"""
        if endpoint not in self.metrics:
            self.metrics[endpoint] = {
                "count": 0,
                "total_duration": 0,
                "error_count": 0
            }
        
        self.metrics[endpoint]["count"] += 1
        self.metrics[endpoint]["total_duration"] += duration
        
        if status_code >= 400:
            self.metrics[endpoint]["error_count"] += 1
        
        # 记录慢查询
        if duration > 1.0:  # 超过1秒
            self.logger.warning(
                f"慢查询警告: {endpoint} 耗时 {duration:.2f}s, 状态码: {status_code}"
            )
    
    def get_metrics_summary(self) -> dict:
        """获取性能指标摘要"""
        summary = {}
        for endpoint, data in self.metrics.items():
            avg_duration = data["total_duration"] / data["count"] if data["count"] > 0 else 0
            error_rate = data["error_count"] / data["count"] if data["count"] > 0 else 0
            
            summary[endpoint] = {
                "总调用次数": data["count"],
                "平均响应时间": f"{avg_duration:.3f}s",
                "错误率": f"{error_rate:.2%}",
                "总错误数": data["error_count"]
            }
        
        return summary

# 全局性能监控实例
performance_monitor = PerformanceMonitor()
```

### 15.5 总结和下一步

#### 15.5.1 功能完整性检查清单

- ✅ **数据模型设计**: 完整的改编关系数据模型
- ✅ **API接口实现**: 20+ RESTful API 端点
- ✅ **业务逻辑**: 复杂的改编业务逻辑和验证
- ✅ **权限控制**: 基于 RBAC 的细粒度权限管理
- ✅ **历史追踪**: 完整的改编历史记录系统
- ✅ **缓存优化**: 多层缓存策略和性能优化
- ✅ **错误处理**: 全面的错误处理和用户友好的错误信息
- ✅ **测试覆盖**: 单元测试和集成测试
- ✅ **API文档**: 详细的 OpenAPI 文档和使用示例
- ✅ **部署配置**: 生产环境部署和监控配置

#### 15.5.2 性能指标目标

| 指标 | 目标值 | 监控方式 |
|------|--------|----------|
| API 响应时间 | < 200ms (P95) | APM监控 |
| 数据库查询时间 | < 100ms (P95) | 慢查询日志 |
| 缓存命中率 | > 80% | Redis监控 |
| 系统可用性 | > 99.9% | 健康检查 |
| 错误率 | < 0.1% | 错误日志分析 |

#### 15.5.3 扩展规划

**短期扩展 (1-3个月):**
- 改编内容智能推荐
- 批量操作优化
- 移动端 API 适配
- 国际化支持

**中期扩展 (3-6个月):**
- 改编内容版本控制
- 协作编辑功能
- 高级搜索和过滤
- 数据分析和报表

**长期扩展 (6个月+):**
- AI 辅助改编建议
- 跨平台项目导入导出
- 社区功能集成
- 微服务架构迁移

#### 15.5.4 维护指南

**日常维护任务:**
- 监控系统性能指标
- 检查错误日志和异常
- 备份数据库和缓存
- 更新依赖包和安全补丁

**定期维护任务:**
- 清理过期的历史记录和快照
- 优化数据库索引和查询
- 更新 API 文档和示例
- 进行安全审计和漏洞扫描

**紧急响应流程:**
1. 监控告警触发
2. 快速问题诊断
3. 回滚或热修复
4. 问题根因分析
5. 预防措施制定

---

## 实施建议

### 优先级排序

1. **高优先级**: 任务13 (改编历史追踪) - 完善核心功能
2. **中优先级**: 任务14 (缓存优化) - 提升系统性能  
3. **低优先级**: 任务15 (API文档) - 完善
开发者体验

### 时间安排

- **第1周**: 实施任务13 (改编历史追踪)
- **第2-3周**: 实施任务14 (缓存和性能优化)  
- **第4周**: 完成任务15 (API文档和测试)
- **第5周**: 集成测试和部署准备

### 资源需求

**人力资源:**
- 后端开发工程师: 2人
- 数据库工程师: 1人  
- 测试工程师: 1人
- DevOps工程师: 1人

**技术资源:**
- 开发环境: Docker + PostgreSQL + Redis
- 测试环境: 与生产环境一致的配置
- 生产环境: 高可用集群部署
- 监控工具: Prometheus + Grafana + ELK Stack

### 风险评估

**技术风险:**
- 数据库迁移可能影响现有功能
- 缓存一致性问题
- 高并发下的性能瓶颈

**缓解措施:**
- 充分的测试和回滚计划
- 渐进式部署和灰度发布
- 完善的监控和告警机制

---

## 结论

本文档详细描述了Scratch项目改编功能剩余3个任务的完整实现方案。通过系统化的设计和实施，将为用户提供强大、可靠、高性能的项目改编功能。

### 核心价值

1. **创意传承**: 支持项目的多代改编，促进创意的传播和演进
2. **社区协作**: 建立改编关系网络，增强用户间的协作和学习
3. **知识产权保护**: 完整记录改编关系，保护原作者权益
4. **技术创新**: 采用现代化的架构设计，支持高并发和大规模应用

### 技术亮点

- **多层级改编系统**: 支持复杂的改编关系管理
- **智能循环检测**: 防止无效的循环改编关系
- **高性能缓存**: 多层缓存策略优化查询性能
- **完整的历史追踪**: 详细记录所有改编活动
- **灵活的权限控制**: 基于RBAC的细粒度权限管理

通过这些剩余任务的实施，Scratch项目改编功能将成为一个功能完备、性能优异、用户体验良好的创意协作平台核心组件。