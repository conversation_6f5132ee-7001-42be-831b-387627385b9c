#!/usr/bin/env python3
from sqlalchemy import inspect

from app.db.session import get_sync_db


def check_db():
    db = next(get_sync_db())
    try:
        inspector = inspect(db)
        tables = inspector.get_table_names()
        print("Available tables:", tables)

        if "scratch_products" in tables:
            columns = inspector.get_columns("scratch_products")
            print("\nscratch_products columns:")
            for col in columns:
                print(f"  {col['name']}: {col['type']}")
        else:
            print("scratch_products table does not exist")
    finally:
        db.close()


if __name__ == "__main__":
    check_db()
