# Scratch项目改编功能实施计划与技术文档

## 1. 概述

本文档详细阐述了为Scratch编程平台添加项目改编功能的架构设计、API接口、业务逻辑、安全考虑、性能优化以及具体的实施计划。该功能将允许用户基于现有项目创建副本进行修改，并保留原作者信息和改编关系，从而促进用户创作和内容生态的繁荣。

## 2. 需求分析与改编类型

用户需求：基于现有项目创建副本进行修改，保留原作者信息和改编关系。

改编类型："引用和继承原项目部分内容，创建衍生作品"。这意味着改编后的项目将与原始项目建立引用关系，而不是简单的完全复制。

## 3. 数据模型设计

### 3.1 ScratchProduct表扩展字段

将在 `scratch_products` 表中添加以下字段：

- `original_project_id`: INTEGER, FOREIGN KEY (scratch_products.project_id), NULLABLE=True, COMMENT="直接改编源项目ID"
- `root_project_id`: INTEGER, FOREIGN KEY (scratch_products.project_id), NULLABLE=True, COMMENT="改编链的根项目ID"
- `adapt_level`: INTEGER, DEFAULT=0, NULLABLE=False, COMMENT="改编层级，0为原创"
- `adaptation_type`: VARCHAR(20), DEFAULT='original', NULLABLE=False, COMMENT="改编类型 (original, remix, fork)"

### 3.2 索引优化

为提高查询性能，将添加以下索引：

- `idx_scratch_original_project` ON `scratch_products(original_project_id)`
- `idx_scratch_root_project` ON `scratch_products(root_project_id)`
- `idx_scratch_adapt_level` ON `scratch_products(adapt_level)`
- `idx_scratch_adaptation_type` ON `scratch_products(adaptation_type)`

### 3.3 改编关系约束

- 防止自引用：`CHECK (project_id != original_project_id)`
- 限制改编层级深度：`CHECK (adapt_level >= 0 AND adapt_level <= 5)`

### 3.4 SQLAlchemy模型更新设计

#### ScratchProduct模型扩展

```python
# 在现有ScratchProduct类中添加字段
original_project_id = Column(Integer, ForeignKey("scratch_products.project_id"), 
                           nullable=True, comment="直接改编源项目ID")
root_project_id = Column(Integer, ForeignKey("scratch_products.project_id"), 
                        nullable=True, comment="改编链根项目ID") 
adapt_level = Column(Integer, default=0, nullable=False, comment="改编层级，0为原创")
adaptation_type = Column(String(20), default="original", nullable=False, comment="改编类型")

# 添加自引用关系
original_project = relationship("ScratchProduct", 
                               remote_side=[project_id], 
                               foreign_keys=[original_project_id],
                               backref="direct_adaptations")

root_project = relationship("ScratchProduct", 
                          remote_side=[project_id],
                          foreign_keys=[root_project_id],
                          backref="all_adaptations")
```

#### User模型关系修复

在User模型中添加 `scratch_products` 关系：

```python
scratch_products = relationship("ScratchProduct", back_populates="author")
```

## 4. 数据库迁移脚本设计

### Alembic迁移脚本模板

```python
"""add scratch product adaptation fields

Revision ID: scratch_adaptation_001
Revises: [最新的revision_id]
Create Date: 2025-01-08 01:30:00.000000

"""
from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "scratch_adaptation_001"
down_revision: str | None = "[最新的revision_id]"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """添加Scratch项目改编相关字段"""
    # 添加改编关系字段
    op.add_column('scratch_products', 
        sa.Column('original_project_id', sa.Integer(), 
                 sa.ForeignKey('scratch_products.project_id'), 
                 nullable=True, comment="直接改编源项目ID"))
    
    op.add_column('scratch_products', 
        sa.Column('root_project_id', sa.Integer(), 
                 sa.ForeignKey('scratch_products.project_id'), 
                 nullable=True, comment="改编链根项目ID"))
    
    op.add_column('scratch_products', 
        sa.Column('adapt_level', sa.Integer(), 
                 nullable=False, server_default="0", 
                 comment="改编层级，0为原创"))
    
    op.add_column('scratch_products', 
        sa.Column('adaptation_type', sa.String(20), 
                 nullable=False, server_default="'original'", 
                 comment="改编类型"))
    
    # 创建索引
    op.create_index('idx_scratch_original_project', 'scratch_products', 
                   ['original_project_id'])
    op.create_index('idx_scratch_root_project', 'scratch_products', 
                   ['root_project_id'])
    op.create_index('idx_scratch_adapt_level', 'scratch_products', 
                   ['adapt_level'])
    op.create_index('idx_scratch_adaptation_type', 'scratch_products', 
                   ['adaptation_type'])
    
    # 添加约束
    op.create_check_constraint('check_no_self_reference', 'scratch_products',
        'project_id != original_project_id')
    op.create_check_constraint('check_adapt_level_limit', 'scratch_products',
        'adapt_level >= 0 AND adapt_level <= 5')


def downgrade() -> None:
    """回滚改编字段"""
    # 删除约束
    op.drop_constraint('check_adapt_level_limit', 'scratch_products')
    op.drop_constraint('check_no_self_reference', 'scratch_products')
    
    # 删除索引
    op.drop_index('idx_scratch_adaptation_type', 'scratch_products')
    op.drop_index('idx_scratch_adapt_level', 'scratch_products')
    op.drop_index('idx_scratch_root_project', 'scratch_products')
    op.drop_index('idx_scratch_original_project', 'scratch_products')
    
    # 删除字段
    op.drop_column('scratch_products', 'adaptation_type')
    op.drop_column('scratch_products', 'adapt_level')
    op.drop_column('scratch_products', 'root_project_id')
    op.drop_column('scratch_products', 'original_project_id')
```

## 5. API接口详细设计

### 5.1 创建改编项目

**POST /api/scratch/{project_id}/adapt**

- **请求参数**：
  - `title`: string, 新项目标题
  - `description`: string, 新项目描述
  - `adaptation_type`: string (`remix` | `fork`), 改编类型
  - `inherit_resources`: boolean, 是否继承原项目资源
  - `inherit_code`: boolean, 是否继承原项目代码
  - `custom_notes`: string, 改编说明
- **响应格式**：返回新创建的改编项目ID、标题、改编类型、原始项目信息、改编层级和创建时间。

### 5.2 获取项目改编列表

**GET /api/scratch/{project_id}/adaptations**

- **查询参数**：
  - `page`: integer, 页码
  - `limit`: integer, 每页数量
  - `adapt_level`: integer, 筛选改编层级
  - `adaptation_type`: string, 筛选改编类型
  - `sort`: string (`created_at` | `adapt_count` | `visit_count`), 排序方式
- **响应格式**：返回改编作品列表、总数和分页信息。

### 5.3 获取改编链路

**GET /api/scratch/{project_id}/ancestry**

- **响应格式**：返回从根项目到当前项目的完整改编链，以及根项目信息。

### 5.4 更新改编设置

**PUT /api/scratch/{project_id}/settings**

- **请求参数**：
  - `can_adapt`: boolean, 是否允许改编
  - `adaptation_policy`: string (`open` | `restricted` | `closed`), 改编策略
  - `max_adapt_level`: integer, 最大允许改编层级
- **响应格式**：返回更新后的项目改编设置。

## 6. 业务逻辑流程设计

### 6.1 改编创建流程

```mermaid
flowchart TD
    A[用户请求改编] --> B{验证权限}
    B -->|失败| C[返回权限错误]
    B -->|成功| D{检查原项目状态}
    D -->|不可改编| E[返回状态错误]
    D -->|可改编| F{检查改编层级}
    F -->|超出限制| G[返回层级错误]
    F -->|允许| H[创建改编项目]
    H --> I[复制项目数据]
    I --> J[建立改编关系]
    J --> K[更新统计数据]
    K --> L[返回成功结果]
```

### 6.2 权限验证逻辑

- 检查原项目 `can_adapt` 和 `is_published` 状态。
- 检查用户是否有 `scratch_create` 权限。
- 检查改编层级是否超出 `MAX_ADAPT_LEVEL`。
- 检查是否会创建循环引用。

### 6.3 改编数据复制逻辑

- 创建新项目，设置 `original_project_id`、`root_project_id`、`adapt_level` 和 `adaptation_type`。
- 根据请求参数选择性继承原项目资源和代码。
- 更新原项目的 `adapt_count` 统计。

## 7. 安全控制机制

- **防止循环引用**：数据库约束和应用层逻辑双重检查。
- **改编层级限制**：全局最大层级限制（5层），项目可自定义。
- **权限控制矩阵**：明确定义不同角色（项目作者、普通用户、管理员）对改编操作的权限。

## 8. 性能优化策略

- **数据库查询优化**：为 `original_project_id`、`root_project_id`、`adapt_level` 和 `adaptation_type` 添加索引；使用递归CTE优化改编链查询。
- **缓存策略**：Redis缓存热门项目的改编计数和改编链信息；缓存用户改编权限检查结果。
- **异步处理**：异步更新改编统计数据、异步发送改编通知、异步更新搜索索引。

## 9. 错误处理

- **错误代码定义**：定义如 `ADAPTATION_NOT_ALLOWED`、`ADAPTATION_LEVEL_EXCEEDED` 等错误码。
- **错误响应格式**：统一的JSON错误响应格式，包含错误码、消息和详细信息。

## 10. 监控和分析

- **关键指标**：改编创建成功率、改编层级分布、热门改编项目排行、改编权限拒绝率。
- **日志记录**：详细记录改编操作日志，包含用户ID、原始项目ID、改编项目ID、改编类型和层级。

## 11. 实施计划

### 第一阶段：数据库层
1. 创建新的Alembic迁移脚本，添加 `original_project_id`、`root_project_id`、`adapt_level`、`adaptation_type` 字段
2. 创建相关索引和约束
3. 执行数据库迁移

### 第二阶段：模型层
1. 更新 `app/models/scratch.py` 中的 `ScratchProduct` 模型，添加新字段和自引用关系
2. 修复 `app/models/user.py` 中 `User` 模型缺失的 `scratch_products` 关系

### 第三阶段：Schema层
1. 在 `app/schemas` 目录下创建 `scratch.py`
2. 定义 `ScratchProductCreate`、`ScratchProductUpdate`、`ScratchProductAdapt` 和 `ScratchProductSettings` 等Pydantic Schema

### 第四阶段：CRUD层
1. 在 `app/crud` 目录下创建 `scratch.py`
2. 实现 `CRUDScratchProduct` 类，继承 `CRUDBase`
3. 实现创建、读取、更新、删除ScratchProduct的方法
4. 包含改编相关的逻辑（如改编计数更新）

### 第五阶段：API层
1. 在 `app/api/endpoints` 目录下创建 `scratch.py`
2. 定义改编相关的API端点：
   - `POST /api/scratch/{project_id}/adapt`
   - `GET /api/scratch/{project_id}/adaptations`
   - `GET /api/scratch/{project_id}/ancestry`
   - `PUT /api/scratch/{project_id}/settings`
3. 将新的API路由添加到 `app/api/api.py`

### 第六阶段：业务逻辑层
1. 在CRUD和服务层实现具体的改编业务逻辑
2. 包括权限验证、数据复制、关系建立、统计更新等

### 第七阶段：权限层
1. 在 `app/api/permission_deps.py` 中添加或修改Scratch项目相关的权限依赖

### 第八阶段：测