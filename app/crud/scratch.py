from typing import Any

from sqlalchemy import func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app.crud.base import CRUDBase
from app.models.scratch import ScratchProduct
from app.schemas.scratch import (
    AdaptationType,
    ScratchProductAdapt,
    ScratchProductCreate,
    ScratchProductUpdate,
)


class CRUDScratchProduct(CRUDBase[ScratchProduct, ScratchProductCreate, ScratchProductUpdate]):
    async def get(self, db: AsyncSession, id: Any) -> ScratchProduct | None:
        """获取单个Scratch项目，并预加载关联关系"""
        query = select(self.model).where(self.model.project_id == id)
        query = query.options(
            joinedload(self.model.author),
            joinedload(self.model.original_project),
            joinedload(self.model.root_project),
        )
        result = await db.execute(query)
        return result.unique().scalar_one_or_none()

    async def get_by_title(self, db: AsyncSession, *, title: str) -> ScratchProduct | None:
        """根据标题获取Scratch项目"""
        result = await db.execute(select(self.model).where(self.model.title == title))
        return result.scalar_one_or_none()

    async def get_multi_by_author(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[ScratchProduct]:
        """获取指定作者的Scratch项目列表"""
        result = await db.execute(
            select(self.model)
            .where(self.model.author_id == author_id)
            .options(joinedload(self.model.author))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_published(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> list[ScratchProduct]:
        """获取已发布的Scratch项目列表"""
        result = await db.execute(
            select(self.model)
            .where(self.model.is_published.is_(True))
            .options(joinedload(self.model.author))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_multi_by_ids(self, db: AsyncSession, *, ids: list[int]) -> list[ScratchProduct]:
        """根据ID列表获取多个Scratch项目"""
        if not ids:
            return []
        query = (
            select(self.model)
            .where(self.model.project_id.in_(ids))
            .options(
                joinedload(self.model.author),
                joinedload(self.model.original_project),
                joinedload(self.model.root_project),
            )
        )
        result = await db.execute(query)
        return result.unique().scalars().all()

    async def create(
        self, db: AsyncSession, *, obj_in: ScratchProductCreate, author_id: int, commit: bool = True
    ) -> ScratchProduct:
        """创建Scratch项目"""
        create_data = obj_in.model_dump()
        create_data["author_id"] = author_id

        # 对于原创项目，设置默认的改编相关字段
        create_data.setdefault("adapt_level", 0)
        create_data.setdefault("adaptation_type", AdaptationType.ORIGINAL.value)

        db_obj = self.model(**create_data)
        db.add(db_obj)
        if commit:
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def create_adaptation(
        self,
        db: AsyncSession,
        *,
        original_project: ScratchProduct,
        obj_in: ScratchProductAdapt,
        author_id: int,
        commit: bool = True,
    ) -> ScratchProduct:
        """创建改编项目"""
        create_data = {
            "title": obj_in.title,
            "description": obj_in.description,
            "author_id": author_id,
            "original_project_id": original_project.project_id,
            "root_project_id": original_project.root_project_id or original_project.project_id,
            "adapt_level": original_project.adapt_level + 1,
            "adaptation_type": obj_in.adaptation_type.value,
            "can_adapt": True,  # 默认允许改编
            "is_published": False,  # 默认为草稿状态
        }

        # 如果需要继承封面图
        if obj_in.inherit_resources and original_project.cover_url:
            create_data["cover_url"] = original_project.cover_url

        db_obj = self.model(**create_data)
        db.add(db_obj)

        # 更新原项目的改编计数
        await self.increment_adapt_count(db, project_id=original_project.project_id, commit=False)

        if commit:
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def update_publish_status(
        self, db: AsyncSession, *, db_obj: ScratchProduct, is_published: bool
    ) -> ScratchProduct:
        """更新项目发布状态"""
        db_obj.is_published = is_published
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_drafts(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[ScratchProduct]:
        """获取指定作者的草稿列表"""
        result = await db.execute(
            select(self.model)
            .where(self.model.author_id == author_id)
            .where(self.model.is_published.is_(False))
            .options(joinedload(self.model.author))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_adaptations(
        self,
        db: AsyncSession,
        *,
        original_project_id: int,
        skip: int = 0,
        limit: int = 100,
        adapt_level: int | None = None,
        adaptation_type: AdaptationType | None = None,
    ) -> list[ScratchProduct]:
        """获取指定项目的改编列表"""
        query = select(self.model).where(self.model.original_project_id == original_project_id)

        if adapt_level is not None:
            query = query.where(self.model.adapt_level == adapt_level)

        if adaptation_type is not None:
            query = query.where(self.model.adaptation_type == adaptation_type.value)

        query = query.options(joinedload(self.model.author)).offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def get_all_adaptations(
        self,
        db: AsyncSession,
        *,
        root_project_id: int,
        skip: int = 0,
        limit: int = 100,
    ) -> list[ScratchProduct]:
        """获取改编链中的所有项目"""
        query = (
            select(self.model)
            .where(self.model.root_project_id == root_project_id)
            .options(joinedload(self.model.author))
            .order_by(self.model.adapt_level.asc(), self.model.created_at.asc())
            .offset(skip)
            .limit(limit)
        )

        result = await db.execute(query)
        return result.scalars().all()

    async def get_adaptation_chain(
        self, db: AsyncSession, *, project_id: int
    ) -> list[ScratchProduct]:
        """获取从根项目到指定项目的完整改编链"""
        # 首先获取当前项目
        current_project = await self.get(db, id=project_id)
        if not current_project:
            return []

        # 如果是原创项目，直接返回
        if current_project.adapt_level == 0:
            return [current_project]

        # 构建改编链
        chain = []
        current = current_project

        # 向上追溯到根项目
        while current and current.original_project_id:
            chain.append(current)
            current = await self.get(db, id=current.original_project_id)

        # 添加根项目
        if current:
            chain.append(current)

        # 反转链表，从根项目到当前项目
        return list(reversed(chain))

    async def check_circular_reference(
        self, db: AsyncSession, *, original_project_id: int, new_author_id: int
    ) -> bool:
        """检查是否会创建循环引用"""
        # 获取原项目的改编链
        chain = await self.get_adaptation_chain(db, project_id=original_project_id)

        # 检查新作者是否已经在改编链中有项目
        for project in chain:
            if project.author_id == new_author_id:
                return True

        return False

    async def increment_adapt_count(
        self, db: AsyncSession, *, project_id: int, commit: bool = True
    ) -> None:
        """增加项目的改编计数"""
        stmt = (
            update(self.model)
            .where(self.model.project_id == project_id)
            .values(adapt_count=self.model.adapt_count + 1)
        )
        await db.execute(stmt)
        if commit:
            await db.commit()

    async def increment_visit_count(
        self, db: AsyncSession, *, project_id: int, commit: bool = True
    ) -> None:
        """增加项目的访问计数"""
        stmt = (
            update(self.model)
            .where(self.model.project_id == project_id)
            .values(visit_count=self.model.visit_count + 1)
        )
        await db.execute(stmt)
        if commit:
            await db.commit()

    async def bulk_update_stats(
        self, db: AsyncSession, stats_data: dict[int, dict[str, int]]
    ) -> None:
        """批量更新项目的统计数据"""
        if not stats_data:
            return

        update_statements = []
        for project_id, metrics in stats_data.items():
            allowed_metrics = {
                "visit_count": metrics.get("visit_count"),
                "adapt_count": metrics.get("adapt_count"),
                "favorite_count": metrics.get("favorite_count"),
            }
            # 移除值为 None 的项
            update_values = {k: v for k, v in allowed_metrics.items() if v is not None}

            if update_values:
                stmt = (
                    update(self.model)
                    .where(self.model.project_id == project_id)
                    .values(**update_values)
                )
                update_statements.append(stmt)

        # 一次性执行所有更新
        for stmt in update_statements:
            await db.execute(stmt)

        await db.commit()

    async def get_adaptation_statistics(
        self, db: AsyncSession, *, project_id: int
    ) -> dict[str, Any]:
        """获取项目的改编统计信息"""
        # 获取直接改编数量
        direct_adaptations = await db.execute(
            select(func.count(self.model.project_id)).where(
                self.model.original_project_id == project_id
            )
        )
        direct_count = direct_adaptations.scalar() or 0

        # 获取总改编数量（包括间接改编）
        total_adaptations = await db.execute(
            select(func.count(self.model.project_id)).where(
                self.model.root_project_id == project_id
            )
        )
        total_count = total_adaptations.scalar() or 0

        # 获取各层级的改编数量
        level_stats = await db.execute(
            select(self.model.adapt_level, func.count(self.model.project_id))
            .where(self.model.root_project_id == project_id)
            .group_by(self.model.adapt_level)
        )
        level_distribution = dict(level_stats.fetchall())

        return {
            "direct_adaptations": direct_count,
            "total_adaptations": total_count,
            "level_distribution": level_distribution,
            "max_level": max(level_distribution.keys()) if level_distribution else 0,
        }

    async def search_projects(
        self,
        db: AsyncSession,
        *,
        query: str,
        skip: int = 0,
        limit: int = 100,
        published_only: bool = True,
    ) -> list[ScratchProduct]:
        """搜索Scratch项目"""
        search_query = select(self.model).where(
            or_(
                self.model.title.ilike(f"%{query}%"),
                self.model.description.ilike(f"%{query}%"),
            )
        )

        if published_only:
            search_query = search_query.where(self.model.is_published.is_(True))

        search_query = search_query.options(joinedload(self.model.author)).offset(skip).limit(limit)

        result = await db.execute(search_query)
        return result.scalars().all()


# 创建实例
scratch_product = CRUDScratchProduct(ScratchProduct)
