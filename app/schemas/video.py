import logging
from datetime import datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel, ConfigDict, Field, model_serializer, model_validator

from app.config import get_settings
from app.schemas.category import CategoryBase
from app.schemas.review import ReviewBase
from app.schemas.tag import TagBase
from app.schemas.user import UserAggregated
from app.schemas.video_folder import VideoFolderOut

logger = logging.getLogger(__name__)


settings = get_settings()
CDN_DOMAIN = settings.OSS_CDN_DOMAIN


class VideoStatus(str, Enum):
    """文章状态枚举"""

    ALL = "all"
    DRAFT = "draft"
    PUBLISHED_APPROVED = "published_approved"
    PUBLISHED_PENDING = "published_pending"
    PUBLISHED_REJECTED = "published_rejected"


class ContentStats(BaseModel):
    """内容统计信息"""

    like_count: int = Field(0, description="点赞数")
    favorite_count: int = Field(0, description="收藏数")
    visit_count: int = Field(0, description="访问次数")
    comment_count: int = Field(0, description="评论数")
    share_count: int = Field(0, description="分享数")

    # 用户相关统计
    is_liked_by_user: bool = Field(False, description="当前用户是否已点赞")
    is_favorited_by_user: bool = Field(False, description="当前用户是否已收藏")
    is_followed_author: bool = Field(False, description="当前用户是否已关注作者")


class ContentMeta(BaseModel):
    """内容元数据"""

    content_type: str = Field("video", description="内容类型: article, video")
    content_id: int = Field(..., description="内容ID")
    slug: str | None = Field(None, description="内容别名")
    seo_title: str | None = Field(None, description="SEO标题")
    seo_description: str | None = Field(None, description="SEO描述")
    keywords: list[str] | None = Field(None, description="关键词")

    @model_validator(mode="before")
    def ensure_content_id(cls, values):
        """
        确保 content_id 存在:
        - 优先使用传入的 content_id
        - 如果不存在则尝试从 id 字段填充
        - 如果仍为 None 则使用占位符 0，避免直接抛出全局 500
        """
        if isinstance(values, dict):
            cid = values.get("content_id") or values.get("id")
            if cid is None:
                # 使用占位符，避免 Pydantic 校验报错
                values["content_id"] = 0
            else:
                values["content_id"] = cid
        else:
            cid = getattr(values, "content_id", None) or getattr(values, "id", None)
            if cid is None:
                values.content_id = 0
            else:
                values.content_id = cid
        return values


class VideoBase(BaseModel):
    """视频基础模型"""

    id: int = Field(..., description="视频ID")
    author_id: int = Field(..., description="作者ID")
    folder_id: int = Field(..., description="视频所属文件夹ID")
    title: str = Field(..., description="视频标题")
    slug: str | None = Field(None, description="视频别名")
    description: str | None = Field(None, description="视频描述")
    url: str = Field(..., description="视频URL")
    cover_url: str | None = Field(None, description="视频封面URL")
    duration: int | None = Field(None, description="视频时长（秒）")
    width: int | None = Field(None, description="视频宽度（像素）")
    height: int | None = Field(None, description="视频高度（像素）")
    is_published: bool = Field(False, description="是否发布（草稿状态为False）")
    is_approved: bool = Field(False, description="是否通过审核")
    tags: list[TagBase] | None = Field(None, description="标签列表")
    category: CategoryBase | None = Field(None, description="分类")

    model_config = ConfigDict(from_attributes=True)

    @model_validator(mode="before")
    @classmethod
    def validate_relationships(cls, values):
        """确保关系字段正确转换为Pydantic模型实例"""
        if isinstance(values, dict):
            # 处理tags字段
            if "tags" in values and values["tags"] is not None:
                tags_data = values["tags"]
                if isinstance(tags_data, list):
                    processed_tags = []
                    for tag_item in tags_data:
                        if isinstance(tag_item, dict):
                            try:
                                processed_tags.append(TagBase(**tag_item))
                            except (KeyError, TypeError):
                                processed_tags.append(TagBase(name="", is_default=False))
                        else:
                            processed_tags.append(tag_item)
                    values["tags"] = processed_tags

            # 处理category字段
            if "category" in values and values["category"] is not None:
                category_data = values["category"]
                if isinstance(category_data, dict):
                    try:
                        values["category"] = CategoryBase(**category_data)
                    except (KeyError, TypeError):
                        values["category"] = None

        return values


class VideoCreate(VideoBase):
    """创建视频的请求模型"""

    author_id: int = Field(..., description="作者ID")
    folder_id: int | None = Field(
        None, description="视频所属文件夹ID，如果不指定则使用用户的默认文件夹"
    )


class VideoUpdate(BaseModel):
    """更新视频的请求模型"""

    title: str | None = None
    description: str | None = None
    url: str | None = None
    cover_url: str | None = None
    duration: int | None = None
    width: int | None = None  # 视频宽度（像素）
    height: int | None = None  # 视频高度（像素）
    is_published: bool | None = None
    tags: list[str] | None = None
    category_id: int | None = None
    folder_id: int | None = None  # 添加文件夹ID，支持移动视频


class VideoOut(VideoBase):
    review: ReviewBase | None = Field(None, description="审核信息")
    created_at: datetime | None = Field(None, description="创建时间")
    updated_at: datetime | None = Field(None, description="更新时间")
    stats: ContentStats | None = Field(None, description="统计信息")
    author: UserAggregated | None = Field(None, description="作者信息")
    folder: VideoFolderOut | None = Field(None, description="视频文件夹")

    meta: ContentMeta | None = Field(None, description="元数据")
    extra: dict[str, Any] | None = Field(None, description="扩展字段")

    model_config = ConfigDict(from_attributes=True, exclude={"tags"})

    @model_validator(mode="before")
    def populate_meta(self, values: Any) -> Any:
        """
        在验证之前，根据视频数据动态填充meta字段
        """
        # 将ORM对象或Pydantic模型转成dict
        if not isinstance(values, dict):
            if hasattr(values, "model_dump"):
                values = values.model_dump()
            elif hasattr(values, "__dict__"):
                values = dict(values.__dict__)
            else:
                values = {}

        # 保留原始数据，只添加meta字段
        if "meta" not in values or values["meta"] is None:
            tags = values.get("tags", [])
            keywords = [
                tag.name for tag in tags if hasattr(tag, "name") and isinstance(tag.name, str)
            ]
            content_id = values.get("id")
            slug = values.get("slug")

            values["meta"] = ContentMeta(
                content_type="video",
                content_id=content_id,
                slug=slug,
                keywords=keywords,
            )

        return values

    @model_serializer(mode="wrap")
    def ser_model(self, nxt):
        data = nxt(self)

        # 对视频URL和封面URL添加CDN域名前缀
        url_fields = ["url", "cover_url"]
        for field in url_fields:
            if field in data and data[field]:
                url = data[field]
                if url and isinstance(url, str):
                    # 如果是相对路径（以/开头），添加CDN域名
                    if url.startswith("/"):
                        data[field] = CDN_DOMAIN + url
                    # 如果是steam相关路径但没有域名，也添加CDN域名
                    elif url.startswith("steam/") and not url.startswith(("http://", "https://")):
                        data[field] = CDN_DOMAIN + "/" + url

        return data


class Video(VideoBase):
    pass
