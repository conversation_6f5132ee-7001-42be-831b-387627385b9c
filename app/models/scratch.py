from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc


class ScratchProduct(Base):
    """scratching product"""

    __tablename__ = "scratch_products"

    project_id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), index=True, nullable=False)
    description = Column(Text, nullable=True)
    cover_url = Column(String(512), nullable=True, comment="封面图URL")
    author_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    is_published = Column(Boolean, default=False)
    cache_version = Column(Integer, nullable=False, server_default="1", comment="缓存版本号")
    visit_count = Column(Integer, default=0, comment="访问次数")
    adapt_count = Column(Integer, default=0)
    favorite_count = Column(Integer, default=0)
    # 是否允许改编
    can_adapt = Column(Boolean, default=True)

    # 改编关系字段
    original_project_id = Column(
        Integer,
        ForeignKey("scratch_products.project_id"),
        nullable=True,
        comment="直接改编源项目ID",
    )
    root_project_id = Column(
        Integer, ForeignKey("scratch_products.project_id"), nullable=True, comment="改编链根项目ID"
    )
    adapt_level = Column(Integer, default=0, nullable=False, comment="改编层级，0为原创")
    adaptation_type = Column(String(20), default="original", nullable=False, comment="改编类型")

    created_at = Column(Timestamp, default=now_utc)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc)

    # 关系定义
    author = relationship("User", back_populates="scratch_products")

    # 自引用关系 - 改编关系
    original_project = relationship(
        "ScratchProduct",
        remote_side=[project_id],
        foreign_keys=[original_project_id],
        backref="direct_adaptations",
    )

    root_project = relationship(
        "ScratchProduct",
        remote_side=[project_id],
        foreign_keys=[root_project_id],
        backref="all_adaptations",
    )

    def __repr__(self):
        return f"<Scratch {self.title}>"
