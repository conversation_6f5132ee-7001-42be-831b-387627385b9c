from sqlalchemy import select

from app import crud
from app.core.celery import app
from app.core.logging import logger
from app.db.session import SessionLocal
from app.models.outbox import OutboxMessage
from app.services.service_factory import (
    get_article_cache_service,
    get_user_cache_service,
    get_video_cache_service,
)
from app.utils.bloom_filters import article_bloom_filter


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_update_user_cache(user_id: int):
    """
    异步任务：从数据库获取最新的用户信息并更新到缓存中。
    """
    logger.info(f"开始执行用户缓存更新任务，user_id: {user_id}")
    db = SessionLocal()
    user_cache_service = get_user_cache_service()
    try:
        user = await crud.user.get(db, id=user_id)
        if user:
            # 调用带有版本检查的set方法
            await user_cache_service.set_user(user, check_version=True)
            logger.info(f"成功更新用户缓存，user_id: {user_id}, version: {user.cache_version}")
        else:
            # 如果用户已不存在，则写入空缓存
            await user_cache_service.set_null_cache(user_id)
            logger.warning(f"用户不存在，已写入空缓存，user_id: {user_id}")
    finally:
        await db.close()


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_invalidate_user_cache(user_id: int):
    """
    异步任务：从缓存中删除指定用户的信息。
    """
    logger.info(f"开始执行用户缓存失效任务，user_id: {user_id}")
    user_cache_service = get_user_cache_service()
    await user_cache_service.invalidate_user(user_id)
    logger.info(f"成功使缓存失效，user_id: {user_id}")


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_update_article_cache(article_id: int):
    """
    异步任务：从数据库获取最新的文章信息并更新到缓存中。
    """
    logger.info(f"开始执行文章缓存更新任务，article_id: {article_id}")
    db = SessionLocal()
    article_cache_service = get_article_cache_service()
    try:
        article = await crud.article.get(db, id=article_id)
        if article and not article.is_deleted:
            await article_cache_service.set_article(article, check_version=True)
            # 将文章ID写入 Bloom（不可删除，增量维护）
            try:
                await article_bloom_filter._reserve_filter_if_not_exists()
                await article_bloom_filter.add(str(article_id))
            except Exception as e:
                logger.warning(f"写入文章Bloom失败 article_id={article_id}: {e}")
            logger.info(
                f"成功更新文章缓存，article_id: {article_id}, version: {article.cache_version}"
            )
        else:
            # 如果文章不存在或已删除，则写入空缓存
            await article_cache_service.set_null_cache(article_id)
            logger.warning(f"文章不存在或已删除，已写入空缓存，article_id: {article_id}")
    finally:
        await db.close()


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_invalidate_article_cache(article_id: int):
    """
    异步任务：从缓存中删除指定文章的信息。
    """
    logger.info(f"开始执行文章缓存失效任务，article_id: {article_id}")
    article_cache_service = get_article_cache_service()
    await article_cache_service.invalidate_article(article_id)
    logger.info(f"成功使文章缓存失效，article_id: {article_id}")


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_update_video_cache(video_id: int):
    """
    异步任务：从数据库获取最新的视频信息并更新到缓存中。
    """
    logger.info(f"开始执行视频缓存更新任务，video_id: {video_id}")
    db = SessionLocal()
    video_cache_service = get_video_cache_service()
    try:
        video = await crud.video.get(db, id=video_id)
        if video and not video.is_deleted:
            await video_cache_service.set_entity(video, check_version=True)
            logger.info(f"成功更新视频缓存，video_id: {video_id}, version: {video.cache_version}")
        else:
            # 如果视频不存在或已删除，则写入空缓存
            await video_cache_service.set_null_cache(video_id)
            logger.warning(f"视频不存在或已删除，已写入空缓存，video_id: {video_id}")
    finally:
        await db.close()


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_invalidate_video_cache(video_id: int):
    """
    异步任务：从缓存中删除指定视频的信息。
    """
    logger.info(f"开始执行视频缓存失效任务，video_id: {video_id}")
    video_cache_service = get_video_cache_service()
    await video_cache_service.invalidate_entity(video_id)
    logger.info(f"成功使视频缓存失效，video_id: {video_id}")


# 映射主题到任务
TASK_MAP = {
    "user.created": "app.tasks.cache_tasks.task_update_user_cache",
    "user.updated": "app.tasks.cache_tasks.task_update_user_cache",
    "user.deleted": "app.tasks.cache_tasks.task_invalidate_user_cache",
    "stats.update": "app.tasks.stats_tasks.task_update_stats",
    "article.created": "app.tasks.cache_tasks.task_update_article_cache",
    "article.updated": "app.tasks.cache_tasks.task_update_article_cache",
    "article.deleted": "app.tasks.cache_tasks.task_invalidate_article_cache",
    "video.created": "app.tasks.cache_tasks.task_update_video_cache",
    "video.updated": "app.tasks.cache_tasks.task_update_video_cache",
    "video.deleted": "app.tasks.cache_tasks.task_invalidate_video_cache",
    # 通知相关任务
    "notification.created": "app.tasks.notification_tasks.send_notification_websocket",
    "notification.read": "app.tasks.notification_tasks.update_unread_count",
    "notification.all_read": "app.tasks.notification_tasks.update_unread_count",
    # 点赞和评论通知任务
    "like.created": "app.tasks.notification_tasks.create_like_notification",
    "comment.created": "app.tasks.notification_tasks.create_comment_notification",
    "comment.reply_created": "app.tasks.notification_tasks.create_reply_notification",
}


@app.task(
    name="app.tasks.cache_tasks.task_relay_outbox_messages",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
)
async def task_relay_outbox_messages():
    """
    轮询发件箱表，将待处理的消息转发到相应的Celery任务。
    """
    logger.info("开始执行发件箱消息中继任务")
    db = SessionLocal()
    try:
        stmt = (
            select(OutboxMessage)
            .where(OutboxMessage.status == "pending")
            .order_by(OutboxMessage.created_at)
            .limit(100)  # 每次处理100条
        )
        result = await db.execute(stmt)
        messages = result.scalars().all()

        if not messages:
            logger.info("没有待处理的发件箱消息")
            return

        for msg in messages:
            task_name = TASK_MAP.get(msg.topic)
            if not task_name:
                logger.warning(f"未找到主题 '{msg.topic}' 对应的任务")
                msg.status = "failed"
                continue

            try:
                # 根据不同的主题类型，构建不同的任务参数
                if msg.topic in ["like.created"]:
                    # 点赞通知任务参数
                    args = [
                        msg.payload.get("liker_user_id"),
                        msg.payload.get("content_type"),
                        msg.payload.get("content_id"),
                    ]
                elif msg.topic in ["comment.created"]:
                    # 评论通知任务参数
                    args = [
                        msg.payload.get("comment_id"),
                        msg.payload.get("author_id"),
                        msg.payload.get("content_type"),
                        msg.payload.get("content_id"),
                    ]
                elif msg.topic in ["comment.reply_created"]:
                    # 回复通知任务参数
                    args = [
                        msg.payload.get("comment_id"),
                        msg.payload.get("author_id"),
                        msg.payload.get("reply_to_id"),
                        msg.payload.get("content_type"),
                        msg.payload.get("content_id"),
                    ]
                else:
                    # 原有的任务参数逻辑
                    task_arg = (
                        msg.payload.get("article_id")
                        or msg.payload.get("user_id")
                        or msg.payload.get("video_id")
                        or msg.payload.get("notification_id")
                    )
                    if not task_arg:
                        logger.error(
                            f"消息 {msg.id} (topic: {msg.topic}) 的 payload 中缺少必要参数"
                        )
                        msg.status = "failed"
                        continue
                    args = [task_arg]

                # 检查参数完整性
                if any(arg is None for arg in args):
                    logger.error(f"消息 {msg.id} (topic: {msg.topic}) 的参数不完整: {args}")
                    msg.status = "failed"
                    continue

                celery_task = app.signature(task_name, args=args)
                celery_task.delay()
                msg.status = "sent"
                logger.info(f"成功转发消息 {msg.id} (topic: {msg.topic})")
            except Exception as e:
                logger.error(f"转发消息 {msg.id} 失败: {e}")
                msg.status = "failed"

        await db.commit()

    finally:
        await db.close()
