import logging
from typing import Any

from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud
from app.models.scratch import ScratchProduct
from app.models.user import User
from app.schemas.scratch import (
    AdaptationChain,
    ScratchProductAdapt,
    ScratchProductSettings,
    ScratchProductSimple,
)

logger = logging.getLogger(__name__)

# 全局配置常量
MAX_ADAPT_LEVEL = 5  # 最大改编层级
MIN_ADAPT_INTERVAL = 300  # 最小改编间隔（秒）


class ScratchAdaptationService:
    """Scratch项目改编服务，处理改编相关的复杂业务逻辑"""

    def __init__(self):
        self.crud = crud.scratch_product

    async def validate_adaptation_permission(
        self,
        db: AsyncSession,
        *,
        original_project: ScratchProduct,
        user: User,
    ) -> None:
        """验证改编权限

        Args:
            db: 数据库会话
            original_project: 原始项目
            user: 改编用户

        Raises:
            HTTPException: 当权限验证失败时
        """
        # 检查原项目是否允许改编
        if not original_project.can_adapt:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="该项目不允许改编")

        # 检查原项目是否已发布
        if not original_project.is_published:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="只能改编已发布的项目"
            )

        # 检查用户是否有改编权限
        user_permissions = user.permissions
        if "scratch_create" not in user_permissions and "*" not in user_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="您没有创建Scratch项目的权限"
            )

        # 检查是否是项目作者自己改编
        if original_project.author_id == user.id:
            logger.info(f"用户 {user.id} 改编自己的项目 {original_project.project_id}")

    async def validate_adaptation_constraints(
        self,
        db: AsyncSession,
        *,
        original_project: ScratchProduct,
        user: User,
    ) -> None:
        """验证改编约束条件

        Args:
            db: 数据库会话
            original_project: 原始项目
            user: 改编用户

        Raises:
            HTTPException: 当约束验证失败时
        """
        # 检查改编层级限制
        if original_project.adapt_level >= MAX_ADAPT_LEVEL:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"改编层级不能超过 {MAX_ADAPT_LEVEL} 级",
            )

        # 检查循环引用
        is_circular = await self.crud.check_circular_reference(
            db, original_project_id=original_project.project_id, new_author_id=user.id
        )
        if is_circular:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能改编自己在改编链中的项目，这会创建循环引用",
            )

    async def create_adaptation(
        self,
        db: AsyncSession,
        *,
        original_project_id: int,
        adaptation_data: ScratchProductAdapt,
        user: User,
    ) -> ScratchProduct:
        """创建改编项目

        Args:
            db: 数据库会话
            original_project_id: 原始项目ID
            adaptation_data: 改编数据
            user: 改编用户

        Returns:
            创建的改编项目

        Raises:
            HTTPException: 当创建失败时
        """
        # 获取原始项目
        original_project = await self.crud.get(db, id=original_project_id)
        if not original_project:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="原始项目不存在")

        # 验证权限和约束
        await self.validate_adaptation_permission(db, original_project=original_project, user=user)
        await self.validate_adaptation_constraints(db, original_project=original_project, user=user)

        # 创建改编项目
        try:
            adapted_project = await self.crud.create_adaptation(
                db,
                original_project=original_project,
                obj_in=adaptation_data,
                author_id=user.id,
                commit=True,
            )

            logger.info(
                f"用户 {user.id} 成功改编项目 {original_project_id}，"
                f"新项目ID: {adapted_project.project_id}"
            )

            return adapted_project

        except Exception as e:
            logger.error(f"创建改编项目失败: {str(e)}")
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="创建改编项目失败"
            )

    async def get_adaptation_chain(self, db: AsyncSession, *, project_id: int) -> AdaptationChain:
        """获取改编链

        Args:
            db: 数据库会话
            project_id: 项目ID

        Returns:
            改编链信息

        Raises:
            HTTPException: 当项目不存在时
        """
        # 获取改编链
        chain_projects = await self.crud.get_adaptation_chain(db, project_id=project_id)
        if not chain_projects:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

        # 转换为简化模型
        chain = [
            ScratchProductSimple(
                project_id=project.project_id,
                title=project.title,
                description=project.description,
                cover_url=project.cover_url,
                author_id=project.author_id,
                is_published=project.is_published,
                adapt_level=project.adapt_level,
                adaptation_type=project.adaptation_type,
                created_at=project.created_at,
                author=project.author,
            )
            for project in chain_projects
        ]

        # 获取根项目
        root_project = chain[0] if chain else None
        if not root_project:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="无法获取根项目"
            )

        # 计算当前项目在链中的层级
        current_level = len(chain) - 1

        # 获取总改编数统计
        stats = await self.crud.get_adaptation_statistics(db, project_id=root_project.project_id)

        return AdaptationChain(
            chain=chain,
            root_project=root_project,
            current_level=current_level,
            total_adaptations=stats.get("total_adaptations", 0),
        )

    async def update_adaptation_settings(
        self,
        db: AsyncSession,
        *,
        project_id: int,
        settings: ScratchProductSettings,
        user: User,
    ) -> ScratchProduct:
        """更新项目改编设置

        Args:
            db: 数据库会话
            project_id: 项目ID
            settings: 改编设置
            user: 用户

        Returns:
            更新后的项目

        Raises:
            HTTPException: 当更新失败时
        """
        # 获取项目
        project = await self.crud.get(db, id=project_id)
        if not project:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

        # 检查权限（只有作者或管理员可以修改设置）
        if project.author_id != user.id and not user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="只有项目作者可以修改改编设置"
            )

        # 更新设置
        update_data = {
            "can_adapt": settings.can_adapt,
        }

        try:
            updated_project = await self.crud.update(
                db, db_obj=project, obj_in=update_data, commit=True
            )

            logger.info(
                f"用户 {user.id} 更新项目 {project_id} 的改编设置: can_adapt={settings.can_adapt}"
            )

            return updated_project

        except Exception as e:
            logger.error(f"更新改编设置失败: {str(e)}")
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新改编设置失败"
            )

    async def get_adaptation_statistics(
        self, db: AsyncSession, *, project_id: int
    ) -> dict[str, Any]:
        """获取项目改编统计信息

        Args:
            db: 数据库会话
            project_id: 项目ID

        Returns:
            统计信息

        Raises:
            HTTPException: 当项目不存在时
        """
        # 检查项目是否存在
        project = await self.crud.get(db, id=project_id)
        if not project:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

        # 获取统计信息
        stats = await self.crud.get_adaptation_statistics(db, project_id=project_id)

        return {
            "project_id": project_id,
            "project_title": project.title,
            "is_original": project.adapt_level == 0,
            "adapt_level": project.adapt_level,
            "adaptation_type": project.adaptation_type,
            "can_adapt": project.can_adapt,
            **stats,
        }

    async def copy_project_resources(
        self,
        db: AsyncSession,
        *,
        source_project: ScratchProduct,
        target_project: ScratchProduct,
        inherit_resources: bool = True,
        inherit_code: bool = True,
    ) -> None:
        """复制项目资源（预留接口，实际实现需要根据具体的资源存储方式）

        Args:
            db: 数据库会话
            source_project: 源项目
            target_project: 目标项目
            inherit_resources: 是否继承资源文件
            inherit_code: 是否继承代码
        """
        # 这里是资源复制的预留接口
        # 实际实现需要根据Scratch项目的具体资源存储方式来实现
        # 例如：复制项目文件、图片、音频等资源

        logger.info(
            f"复制项目资源: {source_project.project_id} -> {target_project.project_id}, "
            f"inherit_resources={inherit_resources}, inherit_code={inherit_code}"
        )

        # TODO: 实现具体的资源复制逻辑
        # 1. 复制项目文件
        # 2. 复制媒体资源
        # 3. 更新资源引用路径

        pass


# 创建服务实例
scratch_adaptation_service = ScratchAdaptationService()
