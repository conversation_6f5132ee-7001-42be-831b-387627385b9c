# Scratch项目改编功能数据模型设计

## 1. 改编关系数据模型

### 1.1 ScratchProduct表扩展字段

```sql
-- 添加到现有scratch_products表的新字段
ALTER TABLE scratch_products ADD COLUMN original_project_id INTEGER REFERENCES scratch_products(project_id);
ALTER TABLE scratch_products ADD COLUMN root_project_id INTEGER REFERENCES scratch_products(project_id);
ALTER TABLE scratch_products ADD COLUMN adapt_level INTEGER DEFAULT 0;
ALTER TABLE scratch_products ADD COLUMN adaptation_type VARCHAR(20) DEFAULT 'original';

-- 添加索引优化查询性能
CREATE INDEX idx_scratch_original_project ON scratch_products(original_project_id);
CREATE INDEX idx_scratch_root_project ON scratch_products(root_project_id);
CREATE INDEX idx_scratch_adapt_level ON scratch_products(adapt_level);
```

### 1.2 字段说明

- `original_project_id`: 直接改编源项目ID（NULL表示原创作品）
- `root_project_id`: 改编链的根项目ID（NULL表示原创作品）
- `adapt_level`: 改编层级（0=原创，1=一级改编，2=二级改编...）
- `adaptation_type`: 改编类型枚举
  - `original`: 原创作品
  - `remix`: 改编作品（引用和继承部分内容）
  - `fork`: 完整复制后修改

### 1.3 改编关系约束

```sql
-- 防止自引用
ALTER TABLE scratch_products ADD CONSTRAINT check_no_self_reference 
CHECK (project_id != original_project_id);

-- 限制改编层级深度（最多5层）
ALTER TABLE scratch_products ADD CONSTRAINT check_adapt_level_limit 
CHECK (adapt_level >= 0 AND adapt_level <= 5);
```

## 2. 数据库迁移脚本设计

### 2.1 Alembic迁移脚本模板

```python
"""add scratch product adaptation fields

Revision ID: scratch_adaptation_001
Revises: [最新的revision_id]
Create Date: 2025-01-08 01:30:00.000000

"""
from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "scratch_adaptation_001"
down_revision: str | None = "[最新的revision_id]"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """添加Scratch项目改编相关字段"""
    # 添加改编关系字段
    op.add_column('scratch_products', 
        sa.Column('original_project_id', sa.Integer(), 
                 sa.ForeignKey('scratch_products.project_id'), 
                 nullable=True, comment="直接改编源项目ID"))
    
    op.add_column('scratch_products', 
        sa.Column('root_project_id', sa.Integer(), 
                 sa.ForeignKey('scratch_products.project_id'), 
                 nullable=True, comment="改编链根项目ID"))
    
    op.add_column('scratch_products', 
        sa.Column('adapt_level', sa.Integer(), 
                 nullable=False, server_default="0", 
                 comment="改编层级，0为原创"))
    
    op.add_column('scratch_products', 
        sa.Column('adaptation_type', sa.String(20), 
                 nullable=False, server_default="'original'", 
                 comment="改编类型"))
    
    # 创建索引
    op.create_index('idx_scratch_original_project', 'scratch_products', 
                   ['original_project_id'])
    op.create_index('idx_scratch_root_project', 'scratch_products', 
                   ['root_project_id'])
    op.create_index('idx_scratch_adapt_level', 'scratch_products', 
                   ['adapt_level'])
    op.create_index('idx_scratch_adaptation_type', 'scratch_products', 
                   ['adaptation_type'])
    
    # 添加约束
    op.create_check_constraint('check_no_self_reference', 'scratch_products',
        'project_id != original_project_id')
    op.create_check_constraint('check_adapt_level_limit', 'scratch_products',
        'adapt_level >= 0 AND adapt_level <= 5')


def downgrade() -> None:
    """回滚改编字段"""
    # 删除约束
    op.drop_constraint('check_adapt_level_limit', 'scratch_products')
    op.drop_constraint('check_no_self_reference', 'scratch_products')
    
    # 删除索引
    op.drop_index('idx_scratch_adaptation_type', 'scratch_products')
    op.drop_index('idx_scratch_adapt_level', 'scratch_products')
    op.drop_index('idx_scratch_root_project', 'scratch_products')
    op.drop_index('idx_scratch_original_project', 'scratch_products')
    
    # 删除字段
    op.drop_column('scratch_products', 'adaptation_type')
    op.drop_column('scratch_products', 'adapt_level')
    op.drop_column('scratch_products', 'root_project_id')
    op.drop_column('scratch_products', 'original_project_id')
```

## 3. 改编业务规则

### 3.1 改编权限控制
- 只有`can_adapt=true`且`is_published=true`的项目才能被改编
- 改编者需要有项目创建权限
- 不能改编已删除或私有的项目

### 3.2 改编关系维护
- 创建改编作品时自动设置`original_project_id`和`root_project_id`
- 自动计算`adapt_level`（原项目层级+1）
- 更新原项目的`adapt_count`统计

### 3.3 改编内容继承
- 继承原项目的基础信息（标题作为模板）
- 可选择性继承项目资源和代码
- 保持原作者信息的可见性

## 4. SQLAlchemy模型更新设计

### 4.1 ScratchProduct模型扩展

```python
# 在现有ScratchProduct类中添加字段
original_project_id = Column(Integer, ForeignKey("scratch_products.project_id"), 
                           nullable=True, comment="直接改编源项目ID")
root_project_id = Column(Integer, ForeignKey("scratch_products.project_id"), 
                        nullable=True, comment="改编链根项目ID") 
adapt_level = Column(Integer, default=0, nullable=False, comment="改编层级，0为原创")
adaptation_type = Column(String(20), default="original", nullable=False, comment="改编类型")

# 添加自引用关系
original_project = relationship("ScratchProduct", 
                               remote_side=[project_id], 
                               foreign_keys=[original_project_id],
                               backref="direct_adaptations")

root_project = relationship("ScratchProduct", 
                          remote_side=[project_id],
                          foreign_keys=[root_project_id],
                          backref="all_adaptations")
```

### 4.2 User模型关系修复

```python
# 在User模型中添加缺失的关系
scratch_products = relationship("ScratchProduct", back_populates="author")
```

## 5. API设计概要

### 5.1 核心API端点
```
POST /api/scratch/{project_id}/adapt     # 创建改编作品
GET  /api/scratch/{project_id}/adaptations # 获取改编作品列表
GET  /api/scratch/{project_id}/ancestry    # 获取改编链路
PUT  /api/scratch/{project_id}/settings    # 更新改编设置
```

### 5.2 查询优化
- 使用递归CTE查询完整改编链
- 缓存热门项目的改编关系
- 支持分页和过滤

## 6. 安全考虑

### 6.1 循环引用防护
- 数据库约束防止自引用
- 应用层检查防止循环改编链
- 限制最大改编深度

### 6.2 性能优化
- 索引优化改编关系查询
- 缓存改编统计数据
- 异步更新改编计数

## 7. 实施步骤

1. **数据库层**：执行迁移脚本添加字段和约束
2. **模型层**：更新ScratchProduct和User模型
3. **Schema层**：创建请求和响应的数据结构
4. **CRUD层**：实现改编相关的数据库操作
5. **API层**：实现改编功能的REST接口
6. **业务逻辑层**：实现改编的核心业务规则
7. **测试层**：编写单元测试和集成测试

这个设计支持灵活的项目改编关系，既保持了原作者的权益，又允许创作者基于现有作品进行创新。