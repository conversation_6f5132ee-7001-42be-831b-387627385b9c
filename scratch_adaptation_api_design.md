# Scratch项目改编功能API设计

## 1. API接口详细设计

### 1.1 创建改编项目

**POST /api/scratch/{project_id}/adapt**

创建基于现有项目的改编作品。

#### 请求参数
```json
{
  "title": "我的改编作品",
  "description": "基于原项目的创新改编",
  "adaptation_type": "remix",  // remix | fork
  "inherit_resources": true,   // 是否继承原项目资源
  "inherit_code": true,        // 是否继承原项目代码
  "custom_notes": "改编说明"   // 改编说明
}
```

#### 响应格式
```json
{
  "success": true,
  "data": {
    "project_id": 123,
    "title": "我的改编作品",
    "adaptation_type": "remix",
    "original_project": {
      "project_id": 100,
      "title": "原始项目",
      "author": {
        "id": 1,
        "username": "original_author"
      }
    },
    "adapt_level": 1,
    "created_at": "2025-01-08T01:30:00Z"
  }
}
```

### 1.2 获取项目改编列表

**GET /api/scratch/{project_id}/adaptations**

获取指定项目的所有改编作品。

#### 查询参数
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `adapt_level`: 筛选改编层级
- `adaptation_type`: 筛选改编类型
- `sort`: 排序方式（created_at, adapt_count, visit_count）

#### 响应格式
```json
{
  "success": true,
  "data": {
    "total": 50,
    "items": [
      {
        "project_id": 123,
        "title": "改编作品1",
        "author": {
          "id": 2,
          "username": "adapter1"
        },
        "adaptation_type": "remix",
        "adapt_level": 1,
        "visit_count": 100,
        "adapt_count": 5,
        "created_at": "2025-01-08T01:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 3,
      "has_next": true
    }
  }
}
```

### 1.3 获取改编链路

**GET /api/scratch/{project_id}/ancestry**

获取项目的完整改编链路（从根项目到当前项目）。

#### 响应格式
```json
{
  "success": true,
  "data": {
    "ancestry_chain": [
      {
        "project_id": 1,
        "title": "根项目",
        "author": {"id": 1, "username": "root_author"},
        "adapt_level": 0,
        "adaptation_type": "original"
      },
      {
        "project_id": 50,
        "title": "一级改编",
        "author": {"id": 2, "username": "first_adapter"},
        "adapt_level": 1,
        "adaptation_type": "remix"
      },
      {
        "project_id": 100,
        "title": "当前项目",
        "author": {"id": 3, "username": "current_author"},
        "adapt_level": 2,
        "adaptation_type": "fork"
      }
    ],
    "root_project": {
      "project_id": 1,
      "title": "根项目",
      "total_adaptations": 25
    }
  }
}
```

### 1.4 更新改编设置

**PUT /api/scratch/{project_id}/settings**

更新项目的改编权限设置。

#### 请求参数
```json
{
  "can_adapt": true,           // 是否允许改编
  "adaptation_policy": "open", // open | restricted | closed
  "max_adapt_level": 3         // 最大允许改编层级
}
```

## 2. 业务逻辑流程设计

### 2.1 改编创建流程

```mermaid
flowchart TD
    A[用户请求改编] --> B{验证权限}
    B -->|失败| C[返回权限错误]
    B -->|成功| D{检查原项目状态}
    D -->|不可改编| E[返回状态错误]
    D -->|可改编| F{检查改编层级}
    F -->|超出限制| G[返回层级错误]
    F -->|允许| H[创建改编项目]
    H --> I[复制项目数据]
    I --> J[建立改编关系]
    J --> K[更新统计数据]
    K --> L[返回成功结果]
```

### 2.2 权限验证逻辑

```python
def validate_adaptation_permission(user, original_project):
    """验证改编权限"""
    # 1. 检查原项目是否允许改编
    if not original_project.can_adapt:
        raise AdaptationNotAllowedException("项目不允许改编")
    
    # 2. 检查原项目是否已发布
    if not original_project.is_published:
        raise AdaptationNotAllowedException("只能改编已发布的项目")
    
    # 3. 检查用户是否有创建权限
    if not user.has_permission("scratch_create"):
        raise PermissionDeniedException("缺少项目创建权限")
    
    # 4. 检查改编层级限制
    if original_project.adapt_level >= MAX_ADAPT_LEVEL:
        raise AdaptationLevelExceededException("改编层级超出限制")
    
    # 5. 检查是否存在循环引用风险
    if would_create_cycle(user.id, original_project):
        raise CyclicAdaptationException("检测到潜在的循环改编")
    
    return True
```

### 2.3 改编数据复制逻辑

```python
def create_adapted_project(original_project, adaptation_data, user):
    """创建改编项目"""
    # 1. 创建新项目基础信息
    adapted_project = ScratchProduct(
        title=adaptation_data.title,
        description=adaptation_data.description,
        author_id=user.id,
        original_project_id=original_project.project_id,
        root_project_id=original_project.root_project_id or original_project.project_id,
        adapt_level=original_project.adapt_level + 1,
        adaptation_type=adaptation_data.adaptation_type,
        can_adapt=True,  # 默认允许再次改编
        is_published=False  # 新创建的项目默认未发布
    )
    
    # 2. 继承项目资源（如果需要）
    if adaptation_data.inherit_resources:
        copy_project_resources(original_project, adapted_project)
    
    # 3. 继承项目代码（如果需要）
    if adaptation_data.inherit_code:
        copy_project_code(original_project, adapted_project)
    
    # 4. 更新原项目改编计数
    original_project.adapt_count += 1
    
    return adapted_project
```

## 3. 安全控制机制

### 3.1 防止循环引用

```python
def would_create_cycle(user_id, target_project):
    """检查是否会创建循环引用"""
    # 检查用户是否是改编链中任何项目的作者
    ancestry = get_project_ancestry(target_project)
    for project in ancestry:
        if project.author_id == user_id:
            return True
    return False
```

### 3.2 改编层级限制

- 最大改编层级：5层
- 每个项目可设置自己的最大改编层级
- 系统全局改编层级限制

### 3.3 权限控制矩阵

| 操作 | 项目作者 | 普通用户 | 管理员 |
|------|----------|----------|--------|
| 创建改编 | ✅ | ✅* | ✅ |
| 查看改编列表 | ✅ | ✅ | ✅ |
| 查看改编链 | ✅ | ✅ | ✅ |
| 修改改编设置 | ✅ | ❌ | ✅ |
| 删除改编项目 | ✅ | ❌ | ✅ |

*需要原项目允许改编

## 4. 性能优化策略

### 4.1 数据库查询优化

```sql
-- 改编列表查询优化
SELECT sp.*, u.username, u.avatar 
FROM scratch_products sp
JOIN users u ON sp.author_id = u.id
WHERE sp.original_project_id = ? 
ORDER BY sp.created_at DESC
LIMIT ? OFFSET ?;

-- 改编链查询优化（递归CTE）
WITH RECURSIVE ancestry AS (
    SELECT project_id, title, author_id, original_project_id, adapt_level, 0 as depth
    FROM scratch_products WHERE project_id = ?
    UNION ALL
    SELECT sp.project_id, sp.title, sp.author_id, sp.original_project_id, sp.adapt_level, a.depth + 1
    FROM scratch_products sp
    JOIN ancestry a ON sp.project_id = a.original_project_id
    WHERE a.depth < 10
)
SELECT * FROM ancestry ORDER BY adapt_level;
```

### 4.2 缓存策略

- **项目改编计数缓存**：Redis缓存热门项目的改编数量
- **改编链缓存**：缓存完整的改编链路信息
- **权限缓存**：缓存用户改编权限检查结果

### 4.3 异步处理

- 改编统计更新：异步更新改编计数
- 改编通知：异步发送改编通知给原作者
- 改编索引：异步更新搜索索引

## 5. 错误处理

### 5.1 错误代码定义

```python
class AdaptationErrorCodes:
    ADAPTATION_NOT_ALLOWED = "ADAPT_001"
    ADAPTATION_LEVEL_EXCEEDED = "ADAPT_002" 
    CYCLIC_ADAPTATION = "ADAPT_003"
    PROJECT_NOT_FOUND = "ADAPT_004"
    PERMISSION_DENIED = "ADAPT_005"
    INVALID_ADAPTATION_TYPE = "ADAPT_006"
```

### 5.2 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "ADAPT_001",
    "message": "该项目不允许改编",
    "details": {
      "project_id": 100,
      "can_adapt": false,
      "reason": "作者已禁用改编功能"
    }
  }
}
```

## 6. 监控和分析

### 6.1 关键指标

- 改编创建成功率
- 改编层级分布
- 热门改编项目排行
- 改编权限拒绝率

### 6.2 日志记录

```python
# 改编操作日志
logger.info("Adaptation created", extra={
    "user_id": user.id,
    "original_project_id": original_project.project_id,
    "adapted_project_id": adapted_project.project_id,
    "adaptation_type": adaptation_data.adaptation_type,
    "adapt_level": adapted_project.adapt_level
})
```

这个API设计提供了完整的改编功能支持，包括权限控制、性能优化和安全防护机制。